export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  graphql_public: {
    Tables: {
      [_ in never]: never
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      graphql: {
        Args: {
          extensions?: Json
          operationName?: string
          query?: string
          variables?: Json
        }
        Returns: Json
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
  public: {
    Tables: {
      budget_import: {
        Row: {
          budget_import_id: string
          created_at: string
          created_by_user_id: string
          is_undone: boolean | null
          new_version_id: string
          pre_version_id: string
          project_id: string
          source_filename: string
          source_hash: string
          undone_at: string | null
          undone_by_user_id: string | null
        }
        Insert: {
          budget_import_id?: string
          created_at?: string
          created_by_user_id: string
          is_undone?: boolean | null
          new_version_id: string
          pre_version_id: string
          project_id: string
          source_filename: string
          source_hash: string
          undone_at?: string | null
          undone_by_user_id?: string | null
        }
        Update: {
          budget_import_id?: string
          created_at?: string
          created_by_user_id?: string
          is_undone?: boolean | null
          new_version_id?: string
          pre_version_id?: string
          project_id?: string
          source_filename?: string
          source_hash?: string
          undone_at?: string | null
          undone_by_user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "budget_import_created_by_user_id_fkey"
            columns: ["created_by_user_id"]
            isOneToOne: false
            referencedRelation: "profile"
            referencedColumns: ["user_id"]
          },
          {
            foreignKeyName: "budget_import_new_version_id_fkey"
            columns: ["new_version_id"]
            isOneToOne: false
            referencedRelation: "budget_version"
            referencedColumns: ["budget_version_id"]
          },
          {
            foreignKeyName: "budget_import_pre_version_id_fkey"
            columns: ["pre_version_id"]
            isOneToOne: false
            referencedRelation: "budget_version"
            referencedColumns: ["budget_version_id"]
          },
          {
            foreignKeyName: "budget_import_project_id_fkey"
            columns: ["project_id"]
            isOneToOne: false
            referencedRelation: "project"
            referencedColumns: ["project_id"]
          },
          {
            foreignKeyName: "budget_import_undone_by_user_id_fkey"
            columns: ["undone_by_user_id"]
            isOneToOne: false
            referencedRelation: "profile"
            referencedColumns: ["user_id"]
          },
        ]
      }
      budget_import_audit: {
        Row: {
          audit_id: string
          budget_import_id: string | null
          changed_at: string
          changed_by: string
          created_at: string | null
          created_by_user_id: string | null
          is_undone: boolean | null
          new_values: Json | null
          new_version_id: string | null
          old_values: Json | null
          operation_type: string
          pre_version_id: string | null
          project_id: string | null
          source_filename: string | null
          source_hash: string | null
          undone_at: string | null
          undone_by_user_id: string | null
        }
        Insert: {
          audit_id?: string
          budget_import_id?: string | null
          changed_at?: string
          changed_by: string
          created_at?: string | null
          created_by_user_id?: string | null
          is_undone?: boolean | null
          new_values?: Json | null
          new_version_id?: string | null
          old_values?: Json | null
          operation_type: string
          pre_version_id?: string | null
          project_id?: string | null
          source_filename?: string | null
          source_hash?: string | null
          undone_at?: string | null
          undone_by_user_id?: string | null
        }
        Update: {
          audit_id?: string
          budget_import_id?: string | null
          changed_at?: string
          changed_by?: string
          created_at?: string | null
          created_by_user_id?: string | null
          is_undone?: boolean | null
          new_values?: Json | null
          new_version_id?: string | null
          old_values?: Json | null
          operation_type?: string
          pre_version_id?: string | null
          project_id?: string | null
          source_filename?: string | null
          source_hash?: string | null
          undone_at?: string | null
          undone_by_user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "budget_import_audit_changed_by_fkey"
            columns: ["changed_by"]
            isOneToOne: false
            referencedRelation: "profile"
            referencedColumns: ["user_id"]
          },
        ]
      }
      budget_snapshot: {
        Row: {
          budget_snapshot_id: string
          budget_version_id: string | null
          created_at: string
          created_by_user_id: string
          freeze_date: string
          freeze_reason: string | null
          project_stage_id: string
          updated_at: string
        }
        Insert: {
          budget_snapshot_id?: string
          budget_version_id?: string | null
          created_at?: string
          created_by_user_id: string
          freeze_date?: string
          freeze_reason?: string | null
          project_stage_id: string
          updated_at?: string
        }
        Update: {
          budget_snapshot_id?: string
          budget_version_id?: string | null
          created_at?: string
          created_by_user_id?: string
          freeze_date?: string
          freeze_reason?: string | null
          project_stage_id?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "budget_snapshot_budget_version_id_fkey"
            columns: ["budget_version_id"]
            isOneToOne: false
            referencedRelation: "budget_version"
            referencedColumns: ["budget_version_id"]
          },
          {
            foreignKeyName: "budget_snapshot_created_by_user_id_fkey"
            columns: ["created_by_user_id"]
            isOneToOne: false
            referencedRelation: "profile"
            referencedColumns: ["user_id"]
          },
          {
            foreignKeyName: "budget_snapshot_project_stage_id_fkey"
            columns: ["project_stage_id"]
            isOneToOne: false
            referencedRelation: "project_stage"
            referencedColumns: ["project_stage_id"]
          },
        ]
      }
      budget_snapshot_line_item: {
        Row: {
          budget_snapshot_id: string
          budget_snapshot_line_item_id: string
          cost_certainty: number | null
          created_at: string
          design_certainty: number | null
          factor: number | null
          labor_rate: number | null
          material_rate: number | null
          productivity_per_hour: number | null
          quantity: number | null
          remarks: string | null
          unit: string | null
          unit_rate: number | null
          unit_rate_manual_override: boolean
          updated_at: string
          wbs_library_item_id: string
        }
        Insert: {
          budget_snapshot_id: string
          budget_snapshot_line_item_id?: string
          cost_certainty?: number | null
          created_at?: string
          design_certainty?: number | null
          factor?: number | null
          labor_rate?: number | null
          material_rate?: number | null
          productivity_per_hour?: number | null
          quantity?: number | null
          remarks?: string | null
          unit?: string | null
          unit_rate?: number | null
          unit_rate_manual_override?: boolean
          updated_at?: string
          wbs_library_item_id: string
        }
        Update: {
          budget_snapshot_id?: string
          budget_snapshot_line_item_id?: string
          cost_certainty?: number | null
          created_at?: string
          design_certainty?: number | null
          factor?: number | null
          labor_rate?: number | null
          material_rate?: number | null
          productivity_per_hour?: number | null
          quantity?: number | null
          remarks?: string | null
          unit?: string | null
          unit_rate?: number | null
          unit_rate_manual_override?: boolean
          updated_at?: string
          wbs_library_item_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "budget_snapshot_line_item_budget_snapshot_id_fkey"
            columns: ["budget_snapshot_id"]
            isOneToOne: false
            referencedRelation: "budget_snapshot"
            referencedColumns: ["budget_snapshot_id"]
          },
          {
            foreignKeyName: "budget_snapshot_line_item_wbs_library_item_id_fkey"
            columns: ["wbs_library_item_id"]
            isOneToOne: false
            referencedRelation: "wbs_library_item"
            referencedColumns: ["wbs_library_item_id"]
          },
        ]
      }
      budget_transfer: {
        Row: {
          budget_transfer_id: string
          created_at: string
          created_by_user_id: string
          from_wbs_library_item_id: string
          project_id: string
          reason: string
          tender_line_item_id: string | null
          to_wbs_library_item_id: string
          transfer_amount: number
          updated_at: string
        }
        Insert: {
          budget_transfer_id?: string
          created_at?: string
          created_by_user_id?: string
          from_wbs_library_item_id: string
          project_id: string
          reason: string
          tender_line_item_id?: string | null
          to_wbs_library_item_id: string
          transfer_amount: number
          updated_at?: string
        }
        Update: {
          budget_transfer_id?: string
          created_at?: string
          created_by_user_id?: string
          from_wbs_library_item_id?: string
          project_id?: string
          reason?: string
          tender_line_item_id?: string | null
          to_wbs_library_item_id?: string
          transfer_amount?: number
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "budget_transfer_created_by_user_id_fkey"
            columns: ["created_by_user_id"]
            isOneToOne: false
            referencedRelation: "profile"
            referencedColumns: ["user_id"]
          },
          {
            foreignKeyName: "budget_transfer_from_wbs_library_item_id_fkey"
            columns: ["from_wbs_library_item_id"]
            isOneToOne: false
            referencedRelation: "wbs_library_item"
            referencedColumns: ["wbs_library_item_id"]
          },
          {
            foreignKeyName: "budget_transfer_project_id_fkey"
            columns: ["project_id"]
            isOneToOne: false
            referencedRelation: "project"
            referencedColumns: ["project_id"]
          },
          {
            foreignKeyName: "budget_transfer_tender_line_item_id_fkey"
            columns: ["tender_line_item_id"]
            isOneToOne: false
            referencedRelation: "tender_line_item"
            referencedColumns: ["tender_line_item_id"]
          },
          {
            foreignKeyName: "budget_transfer_to_wbs_library_item_id_fkey"
            columns: ["to_wbs_library_item_id"]
            isOneToOne: false
            referencedRelation: "wbs_library_item"
            referencedColumns: ["wbs_library_item_id"]
          },
        ]
      }
      budget_transfer_audit: {
        Row: {
          audit_id: string
          budget_transfer_id: string
          changed_at: string
          changed_by: string
          created_at: string | null
          created_by_user_id: string | null
          from_wbs_library_item_id: string | null
          new_values: Json | null
          old_values: Json | null
          operation_type: string
          project_id: string | null
          reason: string | null
          tender_line_item_id: string | null
          to_wbs_library_item_id: string | null
          transfer_amount: number | null
          updated_at: string | null
        }
        Insert: {
          audit_id?: string
          budget_transfer_id: string
          changed_at?: string
          changed_by: string
          created_at?: string | null
          created_by_user_id?: string | null
          from_wbs_library_item_id?: string | null
          new_values?: Json | null
          old_values?: Json | null
          operation_type: string
          project_id?: string | null
          reason?: string | null
          tender_line_item_id?: string | null
          to_wbs_library_item_id?: string | null
          transfer_amount?: number | null
          updated_at?: string | null
        }
        Update: {
          audit_id?: string
          budget_transfer_id?: string
          changed_at?: string
          changed_by?: string
          created_at?: string | null
          created_by_user_id?: string | null
          from_wbs_library_item_id?: string | null
          new_values?: Json | null
          old_values?: Json | null
          operation_type?: string
          project_id?: string | null
          reason?: string | null
          tender_line_item_id?: string | null
          to_wbs_library_item_id?: string | null
          transfer_amount?: number | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "budget_transfer_audit_changed_by_fkey"
            columns: ["changed_by"]
            isOneToOne: false
            referencedRelation: "profile"
            referencedColumns: ["user_id"]
          },
        ]
      }
      budget_version: {
        Row: {
          budget_version_id: string
          created_at: string
          created_by_user_id: string
          kind: Database["public"]["Enums"]["budget_version_kind"]
          label: string | null
          prev_version_id: string | null
          project_id: string
          stage_id: string | null
          updated_at: string
        }
        Insert: {
          budget_version_id?: string
          created_at?: string
          created_by_user_id: string
          kind: Database["public"]["Enums"]["budget_version_kind"]
          label?: string | null
          prev_version_id?: string | null
          project_id: string
          stage_id?: string | null
          updated_at?: string
        }
        Update: {
          budget_version_id?: string
          created_at?: string
          created_by_user_id?: string
          kind?: Database["public"]["Enums"]["budget_version_kind"]
          label?: string | null
          prev_version_id?: string | null
          project_id?: string
          stage_id?: string | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "budget_version_created_by_user_id_fkey"
            columns: ["created_by_user_id"]
            isOneToOne: false
            referencedRelation: "profile"
            referencedColumns: ["user_id"]
          },
          {
            foreignKeyName: "budget_version_prev_version_id_fkey"
            columns: ["prev_version_id"]
            isOneToOne: false
            referencedRelation: "budget_version"
            referencedColumns: ["budget_version_id"]
          },
          {
            foreignKeyName: "budget_version_project_id_fkey"
            columns: ["project_id"]
            isOneToOne: false
            referencedRelation: "project"
            referencedColumns: ["project_id"]
          },
          {
            foreignKeyName: "budget_version_stage_id_fkey"
            columns: ["stage_id"]
            isOneToOne: false
            referencedRelation: "project_stage"
            referencedColumns: ["project_stage_id"]
          },
        ]
      }
      budget_version_audit: {
        Row: {
          audit_id: string
          budget_version_id: string | null
          changed_at: string
          changed_by: string
          created_at: string | null
          created_by_user_id: string | null
          kind: Database["public"]["Enums"]["budget_version_kind"] | null
          label: string | null
          new_values: Json | null
          old_values: Json | null
          operation_type: string
          prev_version_id: string | null
          project_id: string | null
          stage_id: string | null
          updated_at: string | null
        }
        Insert: {
          audit_id?: string
          budget_version_id?: string | null
          changed_at?: string
          changed_by: string
          created_at?: string | null
          created_by_user_id?: string | null
          kind?: Database["public"]["Enums"]["budget_version_kind"] | null
          label?: string | null
          new_values?: Json | null
          old_values?: Json | null
          operation_type: string
          prev_version_id?: string | null
          project_id?: string | null
          stage_id?: string | null
          updated_at?: string | null
        }
        Update: {
          audit_id?: string
          budget_version_id?: string | null
          changed_at?: string
          changed_by?: string
          created_at?: string | null
          created_by_user_id?: string | null
          kind?: Database["public"]["Enums"]["budget_version_kind"] | null
          label?: string | null
          new_values?: Json | null
          old_values?: Json | null
          operation_type?: string
          prev_version_id?: string | null
          project_id?: string | null
          stage_id?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "budget_version_audit_changed_by_fkey"
            columns: ["changed_by"]
            isOneToOne: false
            referencedRelation: "profile"
            referencedColumns: ["user_id"]
          },
        ]
      }
      budget_version_item: {
        Row: {
          budget_version_id: string
          budget_version_item_id: string
          cost_certainty: number | null
          created_at: string
          design_certainty: number | null
          factor: number | null
          labor_rate: number | null
          material_rate: number
          productivity_per_hour: number | null
          quantity: number
          remarks: string | null
          unit: string | null
          unit_rate: number
          unit_rate_manual_override: boolean
          updated_at: string
          wbs_library_item_id: string
        }
        Insert: {
          budget_version_id: string
          budget_version_item_id?: string
          cost_certainty?: number | null
          created_at?: string
          design_certainty?: number | null
          factor?: number | null
          labor_rate?: number | null
          material_rate: number
          productivity_per_hour?: number | null
          quantity: number
          remarks?: string | null
          unit?: string | null
          unit_rate: number
          unit_rate_manual_override?: boolean
          updated_at?: string
          wbs_library_item_id: string
        }
        Update: {
          budget_version_id?: string
          budget_version_item_id?: string
          cost_certainty?: number | null
          created_at?: string
          design_certainty?: number | null
          factor?: number | null
          labor_rate?: number | null
          material_rate?: number
          productivity_per_hour?: number | null
          quantity?: number
          remarks?: string | null
          unit?: string | null
          unit_rate?: number
          unit_rate_manual_override?: boolean
          updated_at?: string
          wbs_library_item_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "budget_version_item_version_id_fkey"
            columns: ["budget_version_id"]
            isOneToOne: false
            referencedRelation: "budget_version"
            referencedColumns: ["budget_version_id"]
          },
          {
            foreignKeyName: "budget_version_item_wbs_library_item_id_fkey"
            columns: ["wbs_library_item_id"]
            isOneToOne: false
            referencedRelation: "wbs_library_item"
            referencedColumns: ["wbs_library_item_id"]
          },
        ]
      }
      budget_version_item_audit: {
        Row: {
          audit_id: string
          budget_version_id: string | null
          budget_version_item_id: string | null
          changed_at: string
          changed_by: string
          notes: Json | null
          operation_type: string
          project_id: string | null
          wbs_library_item_id: string | null
        }
        Insert: {
          audit_id?: string
          budget_version_id?: string | null
          budget_version_item_id?: string | null
          changed_at?: string
          changed_by: string
          notes?: Json | null
          operation_type: string
          project_id?: string | null
          wbs_library_item_id?: string | null
        }
        Update: {
          audit_id?: string
          budget_version_id?: string | null
          budget_version_item_id?: string | null
          changed_at?: string
          changed_by?: string
          notes?: Json | null
          operation_type?: string
          project_id?: string | null
          wbs_library_item_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "budget_version_item_audit_changed_by_fkey"
            columns: ["changed_by"]
            isOneToOne: false
            referencedRelation: "profile"
            referencedColumns: ["user_id"]
          },
        ]
      }
      client: {
        Row: {
          client_id: string
          client_url: string | null
          created_at: string
          created_by_user_id: string
          description: string | null
          internal_url: string | null
          internal_url_description: string | null
          logo_url: string | null
          name: string
          org_id: string
          updated_at: string
        }
        Insert: {
          client_id?: string
          client_url?: string | null
          created_at?: string
          created_by_user_id?: string
          description?: string | null
          internal_url?: string | null
          internal_url_description?: string | null
          logo_url?: string | null
          name: string
          org_id: string
          updated_at?: string
        }
        Update: {
          client_id?: string
          client_url?: string | null
          created_at?: string
          created_by_user_id?: string
          description?: string | null
          internal_url?: string | null
          internal_url_description?: string | null
          logo_url?: string | null
          name?: string
          org_id?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "client_created_by_user_id_fkey"
            columns: ["created_by_user_id"]
            isOneToOne: false
            referencedRelation: "profile"
            referencedColumns: ["user_id"]
          },
          {
            foreignKeyName: "client_org_id_fkey"
            columns: ["org_id"]
            isOneToOne: false
            referencedRelation: "organization"
            referencedColumns: ["org_id"]
          },
        ]
      }
      currency: {
        Row: {
          created_at: string
          created_by_user_id: string
          currency_code: string
          description: string
          symbol: string
          symbol_position: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          created_by_user_id?: string
          currency_code: string
          description: string
          symbol: string
          symbol_position?: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          created_by_user_id?: string
          currency_code?: string
          description?: string
          symbol?: string
          symbol_position?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "currency_created_by_user_id_fkey"
            columns: ["created_by_user_id"]
            isOneToOne: false
            referencedRelation: "profile"
            referencedColumns: ["user_id"]
          },
        ]
      }
      gateway_checklist_item: {
        Row: {
          created_at: string
          description: string | null
          gateway_checklist_item_id: string
          name: string
          project_stage_id: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          description?: string | null
          gateway_checklist_item_id?: string
          name: string
          project_stage_id: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          description?: string | null
          gateway_checklist_item_id?: string
          name?: string
          project_stage_id?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "gateway_checklist_item_project_stage_id_fkey"
            columns: ["project_stage_id"]
            isOneToOne: false
            referencedRelation: "project_stage"
            referencedColumns: ["project_stage_id"]
          },
        ]
      }
      gateway_checklist_item_audit: {
        Row: {
          audit_id: string
          changed_at: string
          changed_by: string
          created_at: string | null
          description: string | null
          gateway_checklist_item_id: string | null
          name: string | null
          new_values: Json | null
          old_values: Json | null
          operation_type: string
          project_stage_id: string | null
          updated_at: string | null
        }
        Insert: {
          audit_id?: string
          changed_at?: string
          changed_by: string
          created_at?: string | null
          description?: string | null
          gateway_checklist_item_id?: string | null
          name?: string | null
          new_values?: Json | null
          old_values?: Json | null
          operation_type: string
          project_stage_id?: string | null
          updated_at?: string | null
        }
        Update: {
          audit_id?: string
          changed_at?: string
          changed_by?: string
          created_at?: string | null
          description?: string | null
          gateway_checklist_item_id?: string | null
          name?: string | null
          new_values?: Json | null
          old_values?: Json | null
          operation_type?: string
          project_stage_id?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "gateway_checklist_item_audit_changed_by_fkey"
            columns: ["changed_by"]
            isOneToOne: false
            referencedRelation: "profile"
            referencedColumns: ["user_id"]
          },
        ]
      }
      gateway_checklist_item_status_log: {
        Row: {
          created_at: string
          gateway_checklist_item_id: string
          latest: boolean
          log_id: string
          status: Database["public"]["Enums"]["checklist_item_status"]
          updated_at: string
          updated_by_user_id: string
          valid_at: string
        }
        Insert: {
          created_at?: string
          gateway_checklist_item_id: string
          latest?: boolean
          log_id?: string
          status?: Database["public"]["Enums"]["checklist_item_status"]
          updated_at?: string
          updated_by_user_id?: string
          valid_at?: string
        }
        Update: {
          created_at?: string
          gateway_checklist_item_id?: string
          latest?: boolean
          log_id?: string
          status?: Database["public"]["Enums"]["checklist_item_status"]
          updated_at?: string
          updated_by_user_id?: string
          valid_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "gateway_checklist_item_status_lo_gateway_checklist_item_id_fkey"
            columns: ["gateway_checklist_item_id"]
            isOneToOne: false
            referencedRelation: "gateway_checklist_item"
            referencedColumns: ["gateway_checklist_item_id"]
          },
          {
            foreignKeyName: "gateway_checklist_item_status_log_updated_by_user_id_fkey"
            columns: ["updated_by_user_id"]
            isOneToOne: false
            referencedRelation: "profile"
            referencedColumns: ["user_id"]
          },
        ]
      }
      invite: {
        Row: {
          created_at: string
          expires_at: string
          invite_id: string
          invitee_email: string
          inviter_id: string
          resource_id: string
          resource_type: Database["public"]["Enums"]["invite_resource_type"]
          role: string
          status: Database["public"]["Enums"]["invite_status"]
          token_hash: string
          updated_at: string
          updated_by: string | null
        }
        Insert: {
          created_at?: string
          expires_at: string
          invite_id?: string
          invitee_email: string
          inviter_id: string
          resource_id: string
          resource_type: Database["public"]["Enums"]["invite_resource_type"]
          role: string
          status?: Database["public"]["Enums"]["invite_status"]
          token_hash: string
          updated_at?: string
          updated_by?: string | null
        }
        Update: {
          created_at?: string
          expires_at?: string
          invite_id?: string
          invitee_email?: string
          inviter_id?: string
          resource_id?: string
          resource_type?: Database["public"]["Enums"]["invite_resource_type"]
          role?: string
          status?: Database["public"]["Enums"]["invite_status"]
          token_hash?: string
          updated_at?: string
          updated_by?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "invite_inviter_id_fkey"
            columns: ["inviter_id"]
            isOneToOne: false
            referencedRelation: "profile"
            referencedColumns: ["user_id"]
          },
          {
            foreignKeyName: "invite_updated_by_fkey"
            columns: ["updated_by"]
            isOneToOne: false
            referencedRelation: "profile"
            referencedColumns: ["user_id"]
          },
        ]
      }
      invoice: {
        Row: {
          account: string
          amount: number
          created_at: string
          created_by_user_id: string
          description: string | null
          invoice_date: string
          invoice_id: string
          notes: string | null
          period: string | null
          post_date: string
          purchase_order_id: string
          updated_at: string
        }
        Insert: {
          account: string
          amount: number
          created_at?: string
          created_by_user_id?: string
          description?: string | null
          invoice_date: string
          invoice_id?: string
          notes?: string | null
          period?: string | null
          post_date: string
          purchase_order_id: string
          updated_at?: string
        }
        Update: {
          account?: string
          amount?: number
          created_at?: string
          created_by_user_id?: string
          description?: string | null
          invoice_date?: string
          invoice_id?: string
          notes?: string | null
          period?: string | null
          post_date?: string
          purchase_order_id?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "invoice_created_by_user_id_fkey"
            columns: ["created_by_user_id"]
            isOneToOne: false
            referencedRelation: "profile"
            referencedColumns: ["user_id"]
          },
          {
            foreignKeyName: "invoice_purchase_order_id_fkey"
            columns: ["purchase_order_id"]
            isOneToOne: false
            referencedRelation: "purchase_order"
            referencedColumns: ["purchase_order_id"]
          },
        ]
      }
      invoice_audit: {
        Row: {
          account: string | null
          amount: number | null
          audit_id: string
          changed_at: string
          changed_by: string
          created_at: string | null
          created_by_user_id: string | null
          description: string | null
          invoice_date: string | null
          invoice_id: string | null
          new_values: Json | null
          notes: string | null
          old_values: Json | null
          operation_type: string
          period: string | null
          post_date: string | null
          purchase_order_id: string | null
          updated_at: string | null
        }
        Insert: {
          account?: string | null
          amount?: number | null
          audit_id?: string
          changed_at?: string
          changed_by: string
          created_at?: string | null
          created_by_user_id?: string | null
          description?: string | null
          invoice_date?: string | null
          invoice_id?: string | null
          new_values?: Json | null
          notes?: string | null
          old_values?: Json | null
          operation_type: string
          period?: string | null
          post_date?: string | null
          purchase_order_id?: string | null
          updated_at?: string | null
        }
        Update: {
          account?: string | null
          amount?: number | null
          audit_id?: string
          changed_at?: string
          changed_by?: string
          created_at?: string | null
          created_by_user_id?: string | null
          description?: string | null
          invoice_date?: string | null
          invoice_id?: string | null
          new_values?: Json | null
          notes?: string | null
          old_values?: Json | null
          operation_type?: string
          period?: string | null
          post_date?: string | null
          purchase_order_id?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "invoice_audit_changed_by_fkey"
            columns: ["changed_by"]
            isOneToOne: false
            referencedRelation: "profile"
            referencedColumns: ["user_id"]
          },
        ]
      }
      membership: {
        Row: {
          created_at: string
          entity_id: string
          entity_type: Database["public"]["Enums"]["entity_type"]
          membership_id: string
          role: Database["public"]["Enums"]["membership_role"]
          updated_at: string
          user_id: string
        }
        Insert: {
          created_at?: string
          entity_id: string
          entity_type: Database["public"]["Enums"]["entity_type"]
          membership_id?: string
          role: Database["public"]["Enums"]["membership_role"]
          updated_at?: string
          user_id: string
        }
        Update: {
          created_at?: string
          entity_id?: string
          entity_type?: Database["public"]["Enums"]["entity_type"]
          membership_id?: string
          role?: Database["public"]["Enums"]["membership_role"]
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "membership_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profile"
            referencedColumns: ["user_id"]
          },
        ]
      }
      organization: {
        Row: {
          created_at: string
          created_by_user_id: string
          description: string | null
          logo_url: string | null
          name: string
          org_id: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          created_by_user_id?: string
          description?: string | null
          logo_url?: string | null
          name: string
          org_id?: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          created_by_user_id?: string
          description?: string | null
          logo_url?: string | null
          name?: string
          org_id?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "organization_created_by_user_id_fkey"
            columns: ["created_by_user_id"]
            isOneToOne: false
            referencedRelation: "profile"
            referencedColumns: ["user_id"]
          },
        ]
      }
      profile: {
        Row: {
          avatar_url: string | null
          created_at: string
          email: string
          full_name: string | null
          updated_at: string
          user_id: string
        }
        Insert: {
          avatar_url?: string | null
          created_at?: string
          email: string
          full_name?: string | null
          updated_at?: string
          user_id: string
        }
        Update: {
          avatar_url?: string | null
          created_at?: string
          email?: string
          full_name?: string | null
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
      project: {
        Row: {
          active_budget_version_id: string | null
          client_id: string
          construction_stage_id: string | null
          created_at: string
          created_by_user_id: string
          description: string | null
          name: string
          project_id: string
          updated_at: string
          wbs_library_id: string
        }
        Insert: {
          active_budget_version_id?: string | null
          client_id: string
          construction_stage_id?: string | null
          created_at?: string
          created_by_user_id?: string
          description?: string | null
          name: string
          project_id?: string
          updated_at?: string
          wbs_library_id: string
        }
        Update: {
          active_budget_version_id?: string | null
          client_id?: string
          construction_stage_id?: string | null
          created_at?: string
          created_by_user_id?: string
          description?: string | null
          name?: string
          project_id?: string
          updated_at?: string
          wbs_library_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "project_active_budget_version_id_fkey"
            columns: ["active_budget_version_id"]
            isOneToOne: false
            referencedRelation: "budget_version"
            referencedColumns: ["budget_version_id"]
          },
          {
            foreignKeyName: "project_client_id_fkey"
            columns: ["client_id"]
            isOneToOne: false
            referencedRelation: "client"
            referencedColumns: ["client_id"]
          },
          {
            foreignKeyName: "project_construction_stage_id_fkey"
            columns: ["construction_stage_id"]
            isOneToOne: false
            referencedRelation: "project_stage"
            referencedColumns: ["project_stage_id"]
          },
          {
            foreignKeyName: "project_created_by_user_id_fkey"
            columns: ["created_by_user_id"]
            isOneToOne: false
            referencedRelation: "profile"
            referencedColumns: ["user_id"]
          },
          {
            foreignKeyName: "project_wbs_library_id_fkey"
            columns: ["wbs_library_id"]
            isOneToOne: false
            referencedRelation: "wbs_library"
            referencedColumns: ["wbs_library_id"]
          },
        ]
      }
      project_audit: {
        Row: {
          audit_id: string
          changed_at: string
          changed_by: string
          client_id: string | null
          construction_stage_id: string | null
          created_at: string | null
          created_by_user_id: string | null
          description: string | null
          name: string | null
          new_values: Json | null
          old_values: Json | null
          operation_type: string
          project_id: string | null
          updated_at: string | null
          wbs_library_id: string | null
        }
        Insert: {
          audit_id?: string
          changed_at?: string
          changed_by: string
          client_id?: string | null
          construction_stage_id?: string | null
          created_at?: string | null
          created_by_user_id?: string | null
          description?: string | null
          name?: string | null
          new_values?: Json | null
          old_values?: Json | null
          operation_type: string
          project_id?: string | null
          updated_at?: string | null
          wbs_library_id?: string | null
        }
        Update: {
          audit_id?: string
          changed_at?: string
          changed_by?: string
          client_id?: string | null
          construction_stage_id?: string | null
          created_at?: string | null
          created_by_user_id?: string | null
          description?: string | null
          name?: string | null
          new_values?: Json | null
          old_values?: Json | null
          operation_type?: string
          project_id?: string | null
          updated_at?: string | null
          wbs_library_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "project_audit_changed_by_fkey"
            columns: ["changed_by"]
            isOneToOne: false
            referencedRelation: "profile"
            referencedColumns: ["user_id"]
          },
        ]
      }
      project_gateway_stage_info: {
        Row: {
          above_ground_floors: number | null
          additional_data: Json | null
          ancillary_areas: number | null
          area_of_lowest_floor: number | null
          average_storey_height: number | null
          basement_floors: number | null
          basement_storeys_included_above: number | null
          below_ground_floors: number | null
          circulation_area: number | null
          created_at: string
          created_by_user_id: string
          external_vertical_envelope: number | null
          ground_floor: number | null
          ground_floor_height: number | null
          internal_cube: number | null
          internal_divisions: number | null
          nr_of_storeys: number | null
          nr_of_storeys_primary: number | null
          nr_of_storeys_secondary: number | null
          number_of_units: number | null
          project_gateway_stage_info_id: string
          project_stage_id: string
          site_area: number | null
          spaces_not_enclosed: number | null
          total_gross_internal_floor_area: number | null
          total_gross_internal_floor_area_2: number | null
          updated_at: string
          upper_floors: number | null
          usable_area: number | null
        }
        Insert: {
          above_ground_floors?: number | null
          additional_data?: Json | null
          ancillary_areas?: number | null
          area_of_lowest_floor?: number | null
          average_storey_height?: number | null
          basement_floors?: number | null
          basement_storeys_included_above?: number | null
          below_ground_floors?: number | null
          circulation_area?: number | null
          created_at?: string
          created_by_user_id?: string
          external_vertical_envelope?: number | null
          ground_floor?: number | null
          ground_floor_height?: number | null
          internal_cube?: number | null
          internal_divisions?: number | null
          nr_of_storeys?: number | null
          nr_of_storeys_primary?: number | null
          nr_of_storeys_secondary?: number | null
          number_of_units?: number | null
          project_gateway_stage_info_id?: string
          project_stage_id: string
          site_area?: number | null
          spaces_not_enclosed?: number | null
          total_gross_internal_floor_area?: number | null
          total_gross_internal_floor_area_2?: number | null
          updated_at?: string
          upper_floors?: number | null
          usable_area?: number | null
        }
        Update: {
          above_ground_floors?: number | null
          additional_data?: Json | null
          ancillary_areas?: number | null
          area_of_lowest_floor?: number | null
          average_storey_height?: number | null
          basement_floors?: number | null
          basement_storeys_included_above?: number | null
          below_ground_floors?: number | null
          circulation_area?: number | null
          created_at?: string
          created_by_user_id?: string
          external_vertical_envelope?: number | null
          ground_floor?: number | null
          ground_floor_height?: number | null
          internal_cube?: number | null
          internal_divisions?: number | null
          nr_of_storeys?: number | null
          nr_of_storeys_primary?: number | null
          nr_of_storeys_secondary?: number | null
          number_of_units?: number | null
          project_gateway_stage_info_id?: string
          project_stage_id?: string
          site_area?: number | null
          spaces_not_enclosed?: number | null
          total_gross_internal_floor_area?: number | null
          total_gross_internal_floor_area_2?: number | null
          updated_at?: string
          upper_floors?: number | null
          usable_area?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "project_gateway_stage_info_created_by_user_id_fkey"
            columns: ["created_by_user_id"]
            isOneToOne: false
            referencedRelation: "profile"
            referencedColumns: ["user_id"]
          },
          {
            foreignKeyName: "project_gateway_stage_info_project_stage_id_fkey"
            columns: ["project_stage_id"]
            isOneToOne: true
            referencedRelation: "project_stage"
            referencedColumns: ["project_stage_id"]
          },
        ]
      }
      project_gateway_stage_info_audit: {
        Row: {
          above_ground_floors: number | null
          additional_data: Json | null
          ancillary_areas: number | null
          area_of_lowest_floor: number | null
          audit_id: string
          average_storey_height: number | null
          basement_floors: number | null
          basement_storeys_included_above: number | null
          below_ground_floors: number | null
          changed_at: string
          changed_by: string
          circulation_area: number | null
          created_at: string | null
          created_by_user_id: string | null
          external_vertical_envelope: number | null
          ground_floor: number | null
          ground_floor_height: number | null
          internal_cube: number | null
          internal_divisions: number | null
          new_values: Json | null
          nr_of_storeys: number | null
          nr_of_storeys_primary: number | null
          nr_of_storeys_secondary: number | null
          number_of_units: number | null
          old_values: Json | null
          operation_type: string
          project_gateway_stage_info_id: string | null
          project_stage_id: string | null
          site_area: number | null
          spaces_not_enclosed: number | null
          total_gross_internal_floor_area: number | null
          total_gross_internal_floor_area_2: number | null
          updated_at: string | null
          upper_floors: number | null
          usable_area: number | null
        }
        Insert: {
          above_ground_floors?: number | null
          additional_data?: Json | null
          ancillary_areas?: number | null
          area_of_lowest_floor?: number | null
          audit_id?: string
          average_storey_height?: number | null
          basement_floors?: number | null
          basement_storeys_included_above?: number | null
          below_ground_floors?: number | null
          changed_at?: string
          changed_by: string
          circulation_area?: number | null
          created_at?: string | null
          created_by_user_id?: string | null
          external_vertical_envelope?: number | null
          ground_floor?: number | null
          ground_floor_height?: number | null
          internal_cube?: number | null
          internal_divisions?: number | null
          new_values?: Json | null
          nr_of_storeys?: number | null
          nr_of_storeys_primary?: number | null
          nr_of_storeys_secondary?: number | null
          number_of_units?: number | null
          old_values?: Json | null
          operation_type: string
          project_gateway_stage_info_id?: string | null
          project_stage_id?: string | null
          site_area?: number | null
          spaces_not_enclosed?: number | null
          total_gross_internal_floor_area?: number | null
          total_gross_internal_floor_area_2?: number | null
          updated_at?: string | null
          upper_floors?: number | null
          usable_area?: number | null
        }
        Update: {
          above_ground_floors?: number | null
          additional_data?: Json | null
          ancillary_areas?: number | null
          area_of_lowest_floor?: number | null
          audit_id?: string
          average_storey_height?: number | null
          basement_floors?: number | null
          basement_storeys_included_above?: number | null
          below_ground_floors?: number | null
          changed_at?: string
          changed_by?: string
          circulation_area?: number | null
          created_at?: string | null
          created_by_user_id?: string | null
          external_vertical_envelope?: number | null
          ground_floor?: number | null
          ground_floor_height?: number | null
          internal_cube?: number | null
          internal_divisions?: number | null
          new_values?: Json | null
          nr_of_storeys?: number | null
          nr_of_storeys_primary?: number | null
          nr_of_storeys_secondary?: number | null
          number_of_units?: number | null
          old_values?: Json | null
          operation_type?: string
          project_gateway_stage_info_id?: string | null
          project_stage_id?: string | null
          site_area?: number | null
          spaces_not_enclosed?: number | null
          total_gross_internal_floor_area?: number | null
          total_gross_internal_floor_area_2?: number | null
          updated_at?: string | null
          upper_floors?: number | null
          usable_area?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "project_gateway_stage_info_audit_changed_by_fkey"
            columns: ["changed_by"]
            isOneToOne: false
            referencedRelation: "profile"
            referencedColumns: ["user_id"]
          },
        ]
      }
      project_stage: {
        Row: {
          completion_notes: string | null
          created_at: string
          date_completed: string | null
          date_started: string | null
          description: string | null
          gateway_qualitative_scorecard: Json | null
          name: string
          project_id: string
          project_stage_id: string
          stage: number | null
          stage_order: number
          updated_at: string
        }
        Insert: {
          completion_notes?: string | null
          created_at?: string
          date_completed?: string | null
          date_started?: string | null
          description?: string | null
          gateway_qualitative_scorecard?: Json | null
          name: string
          project_id: string
          project_stage_id?: string
          stage?: number | null
          stage_order: number
          updated_at?: string
        }
        Update: {
          completion_notes?: string | null
          created_at?: string
          date_completed?: string | null
          date_started?: string | null
          description?: string | null
          gateway_qualitative_scorecard?: Json | null
          name?: string
          project_id?: string
          project_stage_id?: string
          stage?: number | null
          stage_order?: number
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "project_stage_project_id_fkey"
            columns: ["project_id"]
            isOneToOne: false
            referencedRelation: "project"
            referencedColumns: ["project_id"]
          },
        ]
      }
      project_stage_audit: {
        Row: {
          audit_id: string
          changed_at: string
          changed_by: string
          completion_notes: string | null
          created_at: string | null
          date_completed: string | null
          date_started: string | null
          description: string | null
          gateway_qualitative_scorecard: Json | null
          name: string | null
          new_values: Json | null
          old_values: Json | null
          operation_type: string
          project_id: string | null
          project_stage_id: string | null
          stage: number | null
          stage_order: number | null
          updated_at: string | null
        }
        Insert: {
          audit_id?: string
          changed_at?: string
          changed_by: string
          completion_notes?: string | null
          created_at?: string | null
          date_completed?: string | null
          date_started?: string | null
          description?: string | null
          gateway_qualitative_scorecard?: Json | null
          name?: string | null
          new_values?: Json | null
          old_values?: Json | null
          operation_type: string
          project_id?: string | null
          project_stage_id?: string | null
          stage?: number | null
          stage_order?: number | null
          updated_at?: string | null
        }
        Update: {
          audit_id?: string
          changed_at?: string
          changed_by?: string
          completion_notes?: string | null
          created_at?: string | null
          date_completed?: string | null
          date_started?: string | null
          description?: string | null
          gateway_qualitative_scorecard?: Json | null
          name?: string | null
          new_values?: Json | null
          old_values?: Json | null
          operation_type?: string
          project_id?: string | null
          project_stage_id?: string | null
          stage?: number | null
          stage_order?: number | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "project_stage_audit_changed_by_fkey"
            columns: ["changed_by"]
            isOneToOne: false
            referencedRelation: "profile"
            referencedColumns: ["user_id"]
          },
        ]
      }
      purchase_order: {
        Row: {
          account: string | null
          co_amount: number | null
          created_at: string
          created_by_user_id: string
          description: string | null
          freight: number | null
          notes: string | null
          original_amount: number | null
          other: number | null
          po_date: string
          po_number: string
          project_id: string
          purchase_order_id: string
          tax: number | null
          updated_at: string
          vendor_id: string
          work_package_id: string
        }
        Insert: {
          account?: string | null
          co_amount?: number | null
          created_at?: string
          created_by_user_id?: string
          description?: string | null
          freight?: number | null
          notes?: string | null
          original_amount?: number | null
          other?: number | null
          po_date: string
          po_number: string
          project_id: string
          purchase_order_id?: string
          tax?: number | null
          updated_at?: string
          vendor_id: string
          work_package_id: string
        }
        Update: {
          account?: string | null
          co_amount?: number | null
          created_at?: string
          created_by_user_id?: string
          description?: string | null
          freight?: number | null
          notes?: string | null
          original_amount?: number | null
          other?: number | null
          po_date?: string
          po_number?: string
          project_id?: string
          purchase_order_id?: string
          tax?: number | null
          updated_at?: string
          vendor_id?: string
          work_package_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "purchase_order_created_by_user_id_fkey"
            columns: ["created_by_user_id"]
            isOneToOne: false
            referencedRelation: "profile"
            referencedColumns: ["user_id"]
          },
          {
            foreignKeyName: "purchase_order_project_id_fkey"
            columns: ["project_id"]
            isOneToOne: false
            referencedRelation: "project"
            referencedColumns: ["project_id"]
          },
          {
            foreignKeyName: "purchase_order_vendor_id_fkey"
            columns: ["vendor_id"]
            isOneToOne: false
            referencedRelation: "vendor"
            referencedColumns: ["vendor_id"]
          },
          {
            foreignKeyName: "purchase_order_work_package_id_fkey"
            columns: ["work_package_id"]
            isOneToOne: false
            referencedRelation: "work_package"
            referencedColumns: ["work_package_id"]
          },
        ]
      }
      purchase_order_audit: {
        Row: {
          account: string | null
          audit_id: string
          changed_at: string
          changed_by: string
          co_amount: number | null
          created_at: string | null
          created_by_user_id: string | null
          description: string | null
          freight: number | null
          new_values: Json | null
          notes: string | null
          old_values: Json | null
          operation_type: string
          original_amount: number | null
          other: number | null
          po_date: string | null
          po_number: string | null
          project_id: string | null
          purchase_order_id: string | null
          tax: number | null
          updated_at: string | null
          vendor_id: string | null
          work_package_id: string | null
        }
        Insert: {
          account?: string | null
          audit_id?: string
          changed_at?: string
          changed_by: string
          co_amount?: number | null
          created_at?: string | null
          created_by_user_id?: string | null
          description?: string | null
          freight?: number | null
          new_values?: Json | null
          notes?: string | null
          old_values?: Json | null
          operation_type: string
          original_amount?: number | null
          other?: number | null
          po_date?: string | null
          po_number?: string | null
          project_id?: string | null
          purchase_order_id?: string | null
          tax?: number | null
          updated_at?: string | null
          vendor_id?: string | null
          work_package_id?: string | null
        }
        Update: {
          account?: string | null
          audit_id?: string
          changed_at?: string
          changed_by?: string
          co_amount?: number | null
          created_at?: string | null
          created_by_user_id?: string | null
          description?: string | null
          freight?: number | null
          new_values?: Json | null
          notes?: string | null
          old_values?: Json | null
          operation_type?: string
          original_amount?: number | null
          other?: number | null
          po_date?: string | null
          po_number?: string | null
          project_id?: string | null
          purchase_order_id?: string | null
          tax?: number | null
          updated_at?: string | null
          vendor_id?: string | null
          work_package_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "purchase_order_audit_changed_by_fkey"
            columns: ["changed_by"]
            isOneToOne: false
            referencedRelation: "profile"
            referencedColumns: ["user_id"]
          },
        ]
      }
      risk_register: {
        Row: {
          cause: string | null
          construction_total: number | null
          created_at: string
          date_for_review: string | null
          date_identified: string
          description: string
          design_fees: number | null
          effect: string | null
          mitigation_plan: string | null
          program_impact: string | null
          project_id: string
          risk_id: string
          risk_owner_email: string | null
          risk_owner_name: string | null
          risk_owner_user_id: string | null
          status: Database["public"]["Enums"]["risk_status"]
          title: string
          updated_at: string
          wbs_library_item_id: string | null
        }
        Insert: {
          cause?: string | null
          construction_total?: number | null
          created_at?: string
          date_for_review?: string | null
          date_identified?: string
          description: string
          design_fees?: number | null
          effect?: string | null
          mitigation_plan?: string | null
          program_impact?: string | null
          project_id: string
          risk_id?: string
          risk_owner_email?: string | null
          risk_owner_name?: string | null
          risk_owner_user_id?: string | null
          status?: Database["public"]["Enums"]["risk_status"]
          title: string
          updated_at?: string
          wbs_library_item_id?: string | null
        }
        Update: {
          cause?: string | null
          construction_total?: number | null
          created_at?: string
          date_for_review?: string | null
          date_identified?: string
          description?: string
          design_fees?: number | null
          effect?: string | null
          mitigation_plan?: string | null
          program_impact?: string | null
          project_id?: string
          risk_id?: string
          risk_owner_email?: string | null
          risk_owner_name?: string | null
          risk_owner_user_id?: string | null
          status?: Database["public"]["Enums"]["risk_status"]
          title?: string
          updated_at?: string
          wbs_library_item_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "risk_register_project_id_fkey"
            columns: ["project_id"]
            isOneToOne: false
            referencedRelation: "project"
            referencedColumns: ["project_id"]
          },
          {
            foreignKeyName: "risk_register_risk_owner_user_id_fkey"
            columns: ["risk_owner_user_id"]
            isOneToOne: false
            referencedRelation: "profile"
            referencedColumns: ["user_id"]
          },
          {
            foreignKeyName: "risk_register_wbs_library_item_id_fkey"
            columns: ["wbs_library_item_id"]
            isOneToOne: false
            referencedRelation: "wbs_library_item"
            referencedColumns: ["wbs_library_item_id"]
          },
        ]
      }
      risk_register_audit: {
        Row: {
          audit_id: string
          cause: string | null
          changed_at: string
          changed_by: string
          construction_total: number | null
          created_at: string | null
          date_for_review: string | null
          date_identified: string | null
          description: string | null
          design_fees: number | null
          effect: string | null
          mitigation_plan: string | null
          new_values: Json | null
          old_values: Json | null
          operation_type: string
          program_impact: string | null
          project_id: string | null
          risk_id: string | null
          risk_owner_email: string | null
          risk_owner_name: string | null
          risk_owner_user_id: string | null
          status: string | null
          title: string | null
          updated_at: string | null
          wbs_library_item_id: string | null
        }
        Insert: {
          audit_id?: string
          cause?: string | null
          changed_at?: string
          changed_by: string
          construction_total?: number | null
          created_at?: string | null
          date_for_review?: string | null
          date_identified?: string | null
          description?: string | null
          design_fees?: number | null
          effect?: string | null
          mitigation_plan?: string | null
          new_values?: Json | null
          old_values?: Json | null
          operation_type: string
          program_impact?: string | null
          project_id?: string | null
          risk_id?: string | null
          risk_owner_email?: string | null
          risk_owner_name?: string | null
          risk_owner_user_id?: string | null
          status?: string | null
          title?: string | null
          updated_at?: string | null
          wbs_library_item_id?: string | null
        }
        Update: {
          audit_id?: string
          cause?: string | null
          changed_at?: string
          changed_by?: string
          construction_total?: number | null
          created_at?: string | null
          date_for_review?: string | null
          date_identified?: string | null
          description?: string | null
          design_fees?: number | null
          effect?: string | null
          mitigation_plan?: string | null
          new_values?: Json | null
          old_values?: Json | null
          operation_type?: string
          program_impact?: string | null
          project_id?: string | null
          risk_id?: string | null
          risk_owner_email?: string | null
          risk_owner_name?: string | null
          risk_owner_user_id?: string | null
          status?: string | null
          title?: string | null
          updated_at?: string | null
          wbs_library_item_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "risk_register_audit_changed_by_fkey"
            columns: ["changed_by"]
            isOneToOne: false
            referencedRelation: "profile"
            referencedColumns: ["user_id"]
          },
        ]
      }
      tender: {
        Row: {
          created_at: string
          created_by_user_id: string
          currency_code: string
          description: string | null
          notes: string | null
          project_id: string
          status: Database["public"]["Enums"]["tender_status"]
          submission_date: string
          tender_id: string
          tender_name: string
          updated_at: string
          vendor_id: string
        }
        Insert: {
          created_at?: string
          created_by_user_id?: string
          currency_code: string
          description?: string | null
          notes?: string | null
          project_id: string
          status?: Database["public"]["Enums"]["tender_status"]
          submission_date: string
          tender_id?: string
          tender_name: string
          updated_at?: string
          vendor_id: string
        }
        Update: {
          created_at?: string
          created_by_user_id?: string
          currency_code?: string
          description?: string | null
          notes?: string | null
          project_id?: string
          status?: Database["public"]["Enums"]["tender_status"]
          submission_date?: string
          tender_id?: string
          tender_name?: string
          updated_at?: string
          vendor_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "tender_created_by_user_id_fkey"
            columns: ["created_by_user_id"]
            isOneToOne: false
            referencedRelation: "profile"
            referencedColumns: ["user_id"]
          },
          {
            foreignKeyName: "tender_currency_code_fkey"
            columns: ["currency_code"]
            isOneToOne: false
            referencedRelation: "currency"
            referencedColumns: ["currency_code"]
          },
          {
            foreignKeyName: "tender_project_id_fkey"
            columns: ["project_id"]
            isOneToOne: false
            referencedRelation: "project"
            referencedColumns: ["project_id"]
          },
          {
            foreignKeyName: "tender_vendor_id_fkey"
            columns: ["vendor_id"]
            isOneToOne: false
            referencedRelation: "vendor"
            referencedColumns: ["vendor_id"]
          },
        ]
      }
      tender_audit: {
        Row: {
          audit_id: string
          changed_at: string
          changed_by: string
          created_at: string | null
          created_by_user_id: string | null
          currency_code: string | null
          description: string | null
          new_values: Json | null
          notes: string | null
          old_values: Json | null
          operation_type: string
          project_id: string | null
          status: Database["public"]["Enums"]["tender_status"] | null
          submission_date: string | null
          tender_id: string
          tender_name: string | null
          updated_at: string | null
          vendor_id: string | null
        }
        Insert: {
          audit_id?: string
          changed_at?: string
          changed_by: string
          created_at?: string | null
          created_by_user_id?: string | null
          currency_code?: string | null
          description?: string | null
          new_values?: Json | null
          notes?: string | null
          old_values?: Json | null
          operation_type: string
          project_id?: string | null
          status?: Database["public"]["Enums"]["tender_status"] | null
          submission_date?: string | null
          tender_id: string
          tender_name?: string | null
          updated_at?: string | null
          vendor_id?: string | null
        }
        Update: {
          audit_id?: string
          changed_at?: string
          changed_by?: string
          created_at?: string | null
          created_by_user_id?: string | null
          currency_code?: string | null
          description?: string | null
          new_values?: Json | null
          notes?: string | null
          old_values?: Json | null
          operation_type?: string
          project_id?: string | null
          status?: Database["public"]["Enums"]["tender_status"] | null
          submission_date?: string | null
          tender_id?: string
          tender_name?: string | null
          updated_at?: string | null
          vendor_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "tender_audit_changed_by_fkey"
            columns: ["changed_by"]
            isOneToOne: false
            referencedRelation: "profile"
            referencedColumns: ["user_id"]
          },
        ]
      }
      tender_line_item: {
        Row: {
          created_at: string
          created_by_user_id: string
          description: string
          labor_rate: number | null
          line_number: number
          material_rate: number | null
          normalization_amount: number | null
          normalization_percentage: number | null
          normalization_type:
            | Database["public"]["Enums"]["normalization_type"]
            | null
          notes: string | null
          productivity_factor: number | null
          quantity: number | null
          subtotal: number
          tender_line_item_id: string
          tender_revision_id: string
          unit: string | null
          unit_rate: number | null
          updated_at: string
        }
        Insert: {
          created_at?: string
          created_by_user_id?: string
          description: string
          labor_rate?: number | null
          line_number: number
          material_rate?: number | null
          normalization_amount?: number | null
          normalization_percentage?: number | null
          normalization_type?:
            | Database["public"]["Enums"]["normalization_type"]
            | null
          notes?: string | null
          productivity_factor?: number | null
          quantity?: number | null
          subtotal: number
          tender_line_item_id?: string
          tender_revision_id: string
          unit?: string | null
          unit_rate?: number | null
          updated_at?: string
        }
        Update: {
          created_at?: string
          created_by_user_id?: string
          description?: string
          labor_rate?: number | null
          line_number?: number
          material_rate?: number | null
          normalization_amount?: number | null
          normalization_percentage?: number | null
          normalization_type?:
            | Database["public"]["Enums"]["normalization_type"]
            | null
          notes?: string | null
          productivity_factor?: number | null
          quantity?: number | null
          subtotal?: number
          tender_line_item_id?: string
          tender_revision_id?: string
          unit?: string | null
          unit_rate?: number | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "tender_line_item_created_by_user_id_fkey"
            columns: ["created_by_user_id"]
            isOneToOne: false
            referencedRelation: "profile"
            referencedColumns: ["user_id"]
          },
          {
            foreignKeyName: "tender_line_item_tender_revision_id_fkey"
            columns: ["tender_revision_id"]
            isOneToOne: false
            referencedRelation: "tender_revision"
            referencedColumns: ["tender_revision_id"]
          },
        ]
      }
      tender_line_item_audit: {
        Row: {
          audit_id: string
          changed_at: string
          changed_by: string
          created_at: string | null
          created_by_user_id: string | null
          description: string | null
          labor_rate: number | null
          line_number: number | null
          material_rate: number | null
          new_values: Json | null
          normalization_amount: number | null
          normalization_percentage: number | null
          normalization_type:
            | Database["public"]["Enums"]["normalization_type"]
            | null
          notes: string | null
          old_values: Json | null
          operation_type: string
          productivity_factor: number | null
          quantity: number | null
          subtotal: number | null
          tender_line_item_id: string
          tender_revision_id: string | null
          unit: string | null
          unit_rate: number | null
          updated_at: string | null
        }
        Insert: {
          audit_id?: string
          changed_at?: string
          changed_by: string
          created_at?: string | null
          created_by_user_id?: string | null
          description?: string | null
          labor_rate?: number | null
          line_number?: number | null
          material_rate?: number | null
          new_values?: Json | null
          normalization_amount?: number | null
          normalization_percentage?: number | null
          normalization_type?:
            | Database["public"]["Enums"]["normalization_type"]
            | null
          notes?: string | null
          old_values?: Json | null
          operation_type: string
          productivity_factor?: number | null
          quantity?: number | null
          subtotal?: number | null
          tender_line_item_id: string
          tender_revision_id?: string | null
          unit?: string | null
          unit_rate?: number | null
          updated_at?: string | null
        }
        Update: {
          audit_id?: string
          changed_at?: string
          changed_by?: string
          created_at?: string | null
          created_by_user_id?: string | null
          description?: string | null
          labor_rate?: number | null
          line_number?: number | null
          material_rate?: number | null
          new_values?: Json | null
          normalization_amount?: number | null
          normalization_percentage?: number | null
          normalization_type?:
            | Database["public"]["Enums"]["normalization_type"]
            | null
          notes?: string | null
          old_values?: Json | null
          operation_type?: string
          productivity_factor?: number | null
          quantity?: number | null
          subtotal?: number | null
          tender_line_item_id?: string
          tender_revision_id?: string | null
          unit?: string | null
          unit_rate?: number | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "tender_line_item_audit_changed_by_fkey"
            columns: ["changed_by"]
            isOneToOne: false
            referencedRelation: "profile"
            referencedColumns: ["user_id"]
          },
        ]
      }
      tender_revision: {
        Row: {
          created_at: string
          created_by_user_id: string
          is_current: boolean
          revision_notes: string | null
          revision_number: number
          tender_id: string
          tender_revision_id: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          created_by_user_id?: string
          is_current?: boolean
          revision_notes?: string | null
          revision_number: number
          tender_id: string
          tender_revision_id?: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          created_by_user_id?: string
          is_current?: boolean
          revision_notes?: string | null
          revision_number?: number
          tender_id?: string
          tender_revision_id?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "tender_revision_created_by_user_id_fkey"
            columns: ["created_by_user_id"]
            isOneToOne: false
            referencedRelation: "profile"
            referencedColumns: ["user_id"]
          },
          {
            foreignKeyName: "tender_revision_tender_id_fkey"
            columns: ["tender_id"]
            isOneToOne: false
            referencedRelation: "tender"
            referencedColumns: ["tender_id"]
          },
        ]
      }
      tender_revision_audit: {
        Row: {
          audit_id: string
          changed_at: string
          changed_by: string
          created_at: string | null
          created_by_user_id: string | null
          is_current: boolean | null
          new_values: Json | null
          old_values: Json | null
          operation_type: string
          revision_notes: string | null
          revision_number: number | null
          tender_id: string | null
          tender_revision_id: string
          updated_at: string | null
        }
        Insert: {
          audit_id?: string
          changed_at?: string
          changed_by: string
          created_at?: string | null
          created_by_user_id?: string | null
          is_current?: boolean | null
          new_values?: Json | null
          old_values?: Json | null
          operation_type: string
          revision_notes?: string | null
          revision_number?: number | null
          tender_id?: string | null
          tender_revision_id: string
          updated_at?: string | null
        }
        Update: {
          audit_id?: string
          changed_at?: string
          changed_by?: string
          created_at?: string | null
          created_by_user_id?: string | null
          is_current?: boolean | null
          new_values?: Json | null
          old_values?: Json | null
          operation_type?: string
          revision_notes?: string | null
          revision_number?: number | null
          tender_id?: string | null
          tender_revision_id?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "tender_revision_audit_changed_by_fkey"
            columns: ["changed_by"]
            isOneToOne: false
            referencedRelation: "profile"
            referencedColumns: ["user_id"]
          },
        ]
      }
      tender_score: {
        Row: {
          comments: string | null
          created_at: string
          created_by_user_id: string
          score: number
          tender_id: string
          tender_score_id: string
          tender_scoring_criteria_id: string
          updated_at: string
        }
        Insert: {
          comments?: string | null
          created_at?: string
          created_by_user_id?: string
          score: number
          tender_id: string
          tender_score_id?: string
          tender_scoring_criteria_id: string
          updated_at?: string
        }
        Update: {
          comments?: string | null
          created_at?: string
          created_by_user_id?: string
          score?: number
          tender_id?: string
          tender_score_id?: string
          tender_scoring_criteria_id?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "tender_score_created_by_user_id_fkey"
            columns: ["created_by_user_id"]
            isOneToOne: false
            referencedRelation: "profile"
            referencedColumns: ["user_id"]
          },
          {
            foreignKeyName: "tender_score_tender_id_fkey"
            columns: ["tender_id"]
            isOneToOne: false
            referencedRelation: "tender"
            referencedColumns: ["tender_id"]
          },
          {
            foreignKeyName: "tender_score_tender_scoring_criteria_id_fkey"
            columns: ["tender_scoring_criteria_id"]
            isOneToOne: false
            referencedRelation: "tender_scoring_criteria"
            referencedColumns: ["tender_scoring_criteria_id"]
          },
        ]
      }
      tender_score_audit: {
        Row: {
          audit_id: string
          changed_at: string
          changed_by: string
          comments: string | null
          created_at: string | null
          created_by_user_id: string | null
          new_values: Json | null
          old_values: Json | null
          operation_type: string
          score: number | null
          tender_id: string | null
          tender_score_id: string
          tender_scoring_criteria_id: string | null
          updated_at: string | null
        }
        Insert: {
          audit_id?: string
          changed_at?: string
          changed_by: string
          comments?: string | null
          created_at?: string | null
          created_by_user_id?: string | null
          new_values?: Json | null
          old_values?: Json | null
          operation_type: string
          score?: number | null
          tender_id?: string | null
          tender_score_id: string
          tender_scoring_criteria_id?: string | null
          updated_at?: string | null
        }
        Update: {
          audit_id?: string
          changed_at?: string
          changed_by?: string
          comments?: string | null
          created_at?: string | null
          created_by_user_id?: string | null
          new_values?: Json | null
          old_values?: Json | null
          operation_type?: string
          score?: number | null
          tender_id?: string | null
          tender_score_id?: string
          tender_scoring_criteria_id?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "tender_score_audit_changed_by_fkey"
            columns: ["changed_by"]
            isOneToOne: false
            referencedRelation: "profile"
            referencedColumns: ["user_id"]
          },
        ]
      }
      tender_scoring_criteria: {
        Row: {
          created_at: string
          created_by_user_id: string
          criteria_name: string
          description: string | null
          is_active: boolean
          max_score: number
          project_id: string
          tender_scoring_criteria_id: string
          updated_at: string
          weight: number
        }
        Insert: {
          created_at?: string
          created_by_user_id?: string
          criteria_name: string
          description?: string | null
          is_active?: boolean
          max_score?: number
          project_id: string
          tender_scoring_criteria_id?: string
          updated_at?: string
          weight: number
        }
        Update: {
          created_at?: string
          created_by_user_id?: string
          criteria_name?: string
          description?: string | null
          is_active?: boolean
          max_score?: number
          project_id?: string
          tender_scoring_criteria_id?: string
          updated_at?: string
          weight?: number
        }
        Relationships: [
          {
            foreignKeyName: "tender_scoring_criteria_created_by_user_id_fkey"
            columns: ["created_by_user_id"]
            isOneToOne: false
            referencedRelation: "profile"
            referencedColumns: ["user_id"]
          },
          {
            foreignKeyName: "tender_scoring_criteria_project_id_fkey"
            columns: ["project_id"]
            isOneToOne: false
            referencedRelation: "project"
            referencedColumns: ["project_id"]
          },
        ]
      }
      tender_scoring_criteria_audit: {
        Row: {
          audit_id: string
          changed_at: string
          changed_by: string
          created_at: string | null
          created_by_user_id: string | null
          criteria_name: string | null
          description: string | null
          is_active: boolean | null
          max_score: number | null
          new_values: Json | null
          old_values: Json | null
          operation_type: string
          project_id: string | null
          tender_scoring_criteria_id: string
          updated_at: string | null
          weight: number | null
        }
        Insert: {
          audit_id?: string
          changed_at?: string
          changed_by: string
          created_at?: string | null
          created_by_user_id?: string | null
          criteria_name?: string | null
          description?: string | null
          is_active?: boolean | null
          max_score?: number | null
          new_values?: Json | null
          old_values?: Json | null
          operation_type: string
          project_id?: string | null
          tender_scoring_criteria_id: string
          updated_at?: string | null
          weight?: number | null
        }
        Update: {
          audit_id?: string
          changed_at?: string
          changed_by?: string
          created_at?: string | null
          created_by_user_id?: string | null
          criteria_name?: string | null
          description?: string | null
          is_active?: boolean | null
          max_score?: number | null
          new_values?: Json | null
          old_values?: Json | null
          operation_type?: string
          project_id?: string | null
          tender_scoring_criteria_id?: string
          updated_at?: string | null
          weight?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "tender_scoring_criteria_audit_changed_by_fkey"
            columns: ["changed_by"]
            isOneToOne: false
            referencedRelation: "profile"
            referencedColumns: ["user_id"]
          },
        ]
      }
      tender_wbs_mapping: {
        Row: {
          coverage_percentage: number | null
          coverage_quantity: number | null
          created_at: string
          created_by_user_id: string
          notes: string | null
          tender_line_item_id: string
          tender_wbs_mapping_id: string
          updated_at: string
          wbs_library_item_id: string
        }
        Insert: {
          coverage_percentage?: number | null
          coverage_quantity?: number | null
          created_at?: string
          created_by_user_id?: string
          notes?: string | null
          tender_line_item_id: string
          tender_wbs_mapping_id?: string
          updated_at?: string
          wbs_library_item_id: string
        }
        Update: {
          coverage_percentage?: number | null
          coverage_quantity?: number | null
          created_at?: string
          created_by_user_id?: string
          notes?: string | null
          tender_line_item_id?: string
          tender_wbs_mapping_id?: string
          updated_at?: string
          wbs_library_item_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "tender_wbs_mapping_created_by_user_id_fkey"
            columns: ["created_by_user_id"]
            isOneToOne: false
            referencedRelation: "profile"
            referencedColumns: ["user_id"]
          },
          {
            foreignKeyName: "tender_wbs_mapping_tender_line_item_id_fkey"
            columns: ["tender_line_item_id"]
            isOneToOne: false
            referencedRelation: "tender_line_item"
            referencedColumns: ["tender_line_item_id"]
          },
          {
            foreignKeyName: "tender_wbs_mapping_wbs_library_item_id_fkey"
            columns: ["wbs_library_item_id"]
            isOneToOne: false
            referencedRelation: "wbs_library_item"
            referencedColumns: ["wbs_library_item_id"]
          },
        ]
      }
      tender_wbs_mapping_audit: {
        Row: {
          audit_id: string
          changed_at: string
          changed_by: string
          coverage_percentage: number | null
          coverage_quantity: number | null
          created_at: string | null
          created_by_user_id: string | null
          new_values: Json | null
          notes: string | null
          old_values: Json | null
          operation_type: string
          tender_line_item_id: string | null
          tender_wbs_mapping_id: string
          updated_at: string | null
          wbs_library_item_id: string | null
        }
        Insert: {
          audit_id?: string
          changed_at?: string
          changed_by: string
          coverage_percentage?: number | null
          coverage_quantity?: number | null
          created_at?: string | null
          created_by_user_id?: string | null
          new_values?: Json | null
          notes?: string | null
          old_values?: Json | null
          operation_type: string
          tender_line_item_id?: string | null
          tender_wbs_mapping_id: string
          updated_at?: string | null
          wbs_library_item_id?: string | null
        }
        Update: {
          audit_id?: string
          changed_at?: string
          changed_by?: string
          coverage_percentage?: number | null
          coverage_quantity?: number | null
          created_at?: string | null
          created_by_user_id?: string | null
          new_values?: Json | null
          notes?: string | null
          old_values?: Json | null
          operation_type?: string
          tender_line_item_id?: string | null
          tender_wbs_mapping_id?: string
          updated_at?: string | null
          wbs_library_item_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "tender_wbs_mapping_audit_changed_by_fkey"
            columns: ["changed_by"]
            isOneToOne: false
            referencedRelation: "profile"
            referencedColumns: ["user_id"]
          },
        ]
      }
      tender_work_package: {
        Row: {
          conversion_date: string
          conversion_notes: string | null
          created_at: string
          created_by_user_id: string
          tender_id: string
          tender_work_package_id: string
          updated_at: string
          work_package_description: string | null
          work_package_name: string
        }
        Insert: {
          conversion_date: string
          conversion_notes?: string | null
          created_at?: string
          created_by_user_id?: string
          tender_id: string
          tender_work_package_id?: string
          updated_at?: string
          work_package_description?: string | null
          work_package_name: string
        }
        Update: {
          conversion_date?: string
          conversion_notes?: string | null
          created_at?: string
          created_by_user_id?: string
          tender_id?: string
          tender_work_package_id?: string
          updated_at?: string
          work_package_description?: string | null
          work_package_name?: string
        }
        Relationships: [
          {
            foreignKeyName: "tender_work_package_created_by_user_id_fkey"
            columns: ["created_by_user_id"]
            isOneToOne: false
            referencedRelation: "profile"
            referencedColumns: ["user_id"]
          },
          {
            foreignKeyName: "tender_work_package_tender_id_fkey"
            columns: ["tender_id"]
            isOneToOne: true
            referencedRelation: "tender"
            referencedColumns: ["tender_id"]
          },
        ]
      }
      tender_work_package_audit: {
        Row: {
          audit_id: string
          changed_at: string
          changed_by: string
          conversion_date: string | null
          conversion_notes: string | null
          created_at: string | null
          created_by_user_id: string | null
          new_values: Json | null
          old_values: Json | null
          operation_type: string
          tender_id: string | null
          tender_work_package_id: string
          updated_at: string | null
          work_package_description: string | null
          work_package_name: string | null
        }
        Insert: {
          audit_id?: string
          changed_at?: string
          changed_by: string
          conversion_date?: string | null
          conversion_notes?: string | null
          created_at?: string | null
          created_by_user_id?: string | null
          new_values?: Json | null
          old_values?: Json | null
          operation_type: string
          tender_id?: string | null
          tender_work_package_id: string
          updated_at?: string | null
          work_package_description?: string | null
          work_package_name?: string | null
        }
        Update: {
          audit_id?: string
          changed_at?: string
          changed_by?: string
          conversion_date?: string | null
          conversion_notes?: string | null
          created_at?: string | null
          created_by_user_id?: string | null
          new_values?: Json | null
          old_values?: Json | null
          operation_type?: string
          tender_id?: string | null
          tender_work_package_id?: string
          updated_at?: string | null
          work_package_description?: string | null
          work_package_name?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "tender_work_package_audit_changed_by_fkey"
            columns: ["changed_by"]
            isOneToOne: false
            referencedRelation: "profile"
            referencedColumns: ["user_id"]
          },
        ]
      }
      vendor: {
        Row: {
          additional_data: Json | null
          certification_info: Json | null
          client_id: string | null
          contact_address: string | null
          contact_email: string | null
          contact_name: string | null
          contact_phone: string | null
          created_at: string
          created_by_user_id: string
          credit_limit: number | null
          currency: string | null
          description: string | null
          insurance_info: Json | null
          is_active: boolean | null
          name: string
          org_id: string | null
          payment_terms: string | null
          payment_terms_days: number | null
          project_id: string | null
          tax_id: string | null
          updated_at: string
          vendor_id: string
          vendor_type: string | null
          website: string | null
        }
        Insert: {
          additional_data?: Json | null
          certification_info?: Json | null
          client_id?: string | null
          contact_address?: string | null
          contact_email?: string | null
          contact_name?: string | null
          contact_phone?: string | null
          created_at?: string
          created_by_user_id?: string
          credit_limit?: number | null
          currency?: string | null
          description?: string | null
          insurance_info?: Json | null
          is_active?: boolean | null
          name: string
          org_id?: string | null
          payment_terms?: string | null
          payment_terms_days?: number | null
          project_id?: string | null
          tax_id?: string | null
          updated_at?: string
          vendor_id?: string
          vendor_type?: string | null
          website?: string | null
        }
        Update: {
          additional_data?: Json | null
          certification_info?: Json | null
          client_id?: string | null
          contact_address?: string | null
          contact_email?: string | null
          contact_name?: string | null
          contact_phone?: string | null
          created_at?: string
          created_by_user_id?: string
          credit_limit?: number | null
          currency?: string | null
          description?: string | null
          insurance_info?: Json | null
          is_active?: boolean | null
          name?: string
          org_id?: string | null
          payment_terms?: string | null
          payment_terms_days?: number | null
          project_id?: string | null
          tax_id?: string | null
          updated_at?: string
          vendor_id?: string
          vendor_type?: string | null
          website?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "vendor_client_id_fkey"
            columns: ["client_id"]
            isOneToOne: false
            referencedRelation: "client"
            referencedColumns: ["client_id"]
          },
          {
            foreignKeyName: "vendor_created_by_user_id_fkey"
            columns: ["created_by_user_id"]
            isOneToOne: false
            referencedRelation: "profile"
            referencedColumns: ["user_id"]
          },
          {
            foreignKeyName: "vendor_org_id_fkey"
            columns: ["org_id"]
            isOneToOne: false
            referencedRelation: "organization"
            referencedColumns: ["org_id"]
          },
          {
            foreignKeyName: "vendor_project_id_fkey"
            columns: ["project_id"]
            isOneToOne: false
            referencedRelation: "project"
            referencedColumns: ["project_id"]
          },
        ]
      }
      vendor_audit: {
        Row: {
          additional_data: Json | null
          audit_id: string
          certification_info: Json | null
          changed_at: string
          changed_by: string
          client_id: string | null
          contact_address: string | null
          contact_email: string | null
          contact_name: string | null
          contact_phone: string | null
          created_at: string | null
          created_by_user_id: string | null
          credit_limit: number | null
          currency: string | null
          description: string | null
          insurance_info: Json | null
          is_active: boolean | null
          name: string | null
          new_values: Json | null
          old_values: Json | null
          operation_type: string
          org_id: string | null
          payment_terms: string | null
          payment_terms_days: number | null
          project_id: string | null
          tax_id: string | null
          updated_at: string | null
          vendor_id: string | null
          vendor_type: string | null
          website: string | null
        }
        Insert: {
          additional_data?: Json | null
          audit_id?: string
          certification_info?: Json | null
          changed_at?: string
          changed_by: string
          client_id?: string | null
          contact_address?: string | null
          contact_email?: string | null
          contact_name?: string | null
          contact_phone?: string | null
          created_at?: string | null
          created_by_user_id?: string | null
          credit_limit?: number | null
          currency?: string | null
          description?: string | null
          insurance_info?: Json | null
          is_active?: boolean | null
          name?: string | null
          new_values?: Json | null
          old_values?: Json | null
          operation_type: string
          org_id?: string | null
          payment_terms?: string | null
          payment_terms_days?: number | null
          project_id?: string | null
          tax_id?: string | null
          updated_at?: string | null
          vendor_id?: string | null
          vendor_type?: string | null
          website?: string | null
        }
        Update: {
          additional_data?: Json | null
          audit_id?: string
          certification_info?: Json | null
          changed_at?: string
          changed_by?: string
          client_id?: string | null
          contact_address?: string | null
          contact_email?: string | null
          contact_name?: string | null
          contact_phone?: string | null
          created_at?: string | null
          created_by_user_id?: string | null
          credit_limit?: number | null
          currency?: string | null
          description?: string | null
          insurance_info?: Json | null
          is_active?: boolean | null
          name?: string | null
          new_values?: Json | null
          old_values?: Json | null
          operation_type?: string
          org_id?: string | null
          payment_terms?: string | null
          payment_terms_days?: number | null
          project_id?: string | null
          tax_id?: string | null
          updated_at?: string | null
          vendor_id?: string | null
          vendor_type?: string | null
          website?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "vendor_audit_changed_by_fkey"
            columns: ["changed_by"]
            isOneToOne: false
            referencedRelation: "profile"
            referencedColumns: ["user_id"]
          },
        ]
      }
      waitlist: {
        Row: {
          created_at: string
          email: string
          waitlist_id: string
        }
        Insert: {
          created_at?: string
          email: string
          waitlist_id?: string
        }
        Update: {
          created_at?: string
          email?: string
          waitlist_id?: string
        }
        Relationships: []
      }
      wbs_library: {
        Row: {
          created_at: string
          description: string | null
          name: string
          updated_at: string
          wbs_library_id: string
        }
        Insert: {
          created_at?: string
          description?: string | null
          name: string
          updated_at?: string
          wbs_library_id?: string
        }
        Update: {
          created_at?: string
          description?: string | null
          name?: string
          updated_at?: string
          wbs_library_id?: string
        }
        Relationships: []
      }
      wbs_library_item: {
        Row: {
          client_id: string | null
          code: string
          cost_scope: string | null
          created_at: string
          description: string
          in_level_code: string
          item_type: Database["public"]["Enums"]["wbs_item_type"]
          level: number
          parent_item_id: string | null
          project_id: string | null
          updated_at: string
          wbs_library_id: string
          wbs_library_item_id: string
        }
        Insert: {
          client_id?: string | null
          code: string
          cost_scope?: string | null
          created_at?: string
          description: string
          in_level_code: string
          item_type?: Database["public"]["Enums"]["wbs_item_type"]
          level: number
          parent_item_id?: string | null
          project_id?: string | null
          updated_at?: string
          wbs_library_id: string
          wbs_library_item_id?: string
        }
        Update: {
          client_id?: string | null
          code?: string
          cost_scope?: string | null
          created_at?: string
          description?: string
          in_level_code?: string
          item_type?: Database["public"]["Enums"]["wbs_item_type"]
          level?: number
          parent_item_id?: string | null
          project_id?: string | null
          updated_at?: string
          wbs_library_id?: string
          wbs_library_item_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "wbs_library_item_client_id_fkey"
            columns: ["client_id"]
            isOneToOne: false
            referencedRelation: "client"
            referencedColumns: ["client_id"]
          },
          {
            foreignKeyName: "wbs_library_item_parent_item_id_fkey"
            columns: ["parent_item_id"]
            isOneToOne: false
            referencedRelation: "wbs_library_item"
            referencedColumns: ["wbs_library_item_id"]
          },
          {
            foreignKeyName: "wbs_library_item_project_id_fkey"
            columns: ["project_id"]
            isOneToOne: false
            referencedRelation: "project"
            referencedColumns: ["project_id"]
          },
          {
            foreignKeyName: "wbs_library_item_wbs_library_id_fkey"
            columns: ["wbs_library_id"]
            isOneToOne: false
            referencedRelation: "wbs_library"
            referencedColumns: ["wbs_library_id"]
          },
        ]
      }
      wbs_library_item_audit: {
        Row: {
          audit_id: string
          changed_at: string
          changed_by: string
          client_id: string | null
          code: string | null
          cost_scope: string | null
          created_at: string | null
          description: string | null
          in_level_code: string | null
          item_type: string | null
          level: number | null
          new_values: Json | null
          old_values: Json | null
          operation_type: string
          parent_item_id: string | null
          project_id: string | null
          updated_at: string | null
          wbs_library_id: string | null
          wbs_library_item_id: string | null
        }
        Insert: {
          audit_id?: string
          changed_at?: string
          changed_by: string
          client_id?: string | null
          code?: string | null
          cost_scope?: string | null
          created_at?: string | null
          description?: string | null
          in_level_code?: string | null
          item_type?: string | null
          level?: number | null
          new_values?: Json | null
          old_values?: Json | null
          operation_type: string
          parent_item_id?: string | null
          project_id?: string | null
          updated_at?: string | null
          wbs_library_id?: string | null
          wbs_library_item_id?: string | null
        }
        Update: {
          audit_id?: string
          changed_at?: string
          changed_by?: string
          client_id?: string | null
          code?: string | null
          cost_scope?: string | null
          created_at?: string | null
          description?: string | null
          in_level_code?: string | null
          item_type?: string | null
          level?: number | null
          new_values?: Json | null
          old_values?: Json | null
          operation_type?: string
          parent_item_id?: string | null
          project_id?: string | null
          updated_at?: string | null
          wbs_library_id?: string | null
          wbs_library_item_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "wbs_library_item_audit_changed_by_fkey"
            columns: ["changed_by"]
            isOneToOne: false
            referencedRelation: "profile"
            referencedColumns: ["user_id"]
          },
        ]
      }
      work_package: {
        Row: {
          created_at: string
          description: string | null
          name: string
          project_id: string
          updated_at: string
          wbs_library_item_id: string
          work_package_id: string
        }
        Insert: {
          created_at?: string
          description?: string | null
          name: string
          project_id: string
          updated_at?: string
          wbs_library_item_id: string
          work_package_id?: string
        }
        Update: {
          created_at?: string
          description?: string | null
          name?: string
          project_id?: string
          updated_at?: string
          wbs_library_item_id?: string
          work_package_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "work_package_project_id_fkey"
            columns: ["project_id"]
            isOneToOne: false
            referencedRelation: "project"
            referencedColumns: ["project_id"]
          },
          {
            foreignKeyName: "work_package_wbs_library_item_id_fkey"
            columns: ["wbs_library_item_id"]
            isOneToOne: false
            referencedRelation: "wbs_library_item"
            referencedColumns: ["wbs_library_item_id"]
          },
        ]
      }
      work_package_audit: {
        Row: {
          audit_id: string
          changed_at: string
          changed_by: string
          created_at: string | null
          description: string | null
          name: string | null
          new_values: Json | null
          old_values: Json | null
          operation_type: string
          project_id: string | null
          updated_at: string | null
          wbs_library_item_id: string | null
          work_package_id: string | null
        }
        Insert: {
          audit_id?: string
          changed_at?: string
          changed_by: string
          created_at?: string | null
          description?: string | null
          name?: string | null
          new_values?: Json | null
          old_values?: Json | null
          operation_type: string
          project_id?: string | null
          updated_at?: string | null
          wbs_library_item_id?: string | null
          work_package_id?: string | null
        }
        Update: {
          audit_id?: string
          changed_at?: string
          changed_by?: string
          created_at?: string | null
          description?: string | null
          name?: string | null
          new_values?: Json | null
          old_values?: Json | null
          operation_type?: string
          project_id?: string | null
          updated_at?: string | null
          wbs_library_item_id?: string | null
          work_package_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "work_package_audit_changed_by_fkey"
            columns: ["changed_by"]
            isOneToOne: false
            referencedRelation: "profile"
            referencedColumns: ["user_id"]
          },
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      accept_invite: {
        Args: { token_param: string }
        Returns: Json
      }
      activate_budget_version: {
        Args: { p_reason?: string; p_version_id: string }
        Returns: boolean
      }
      apply_budget_import: {
        Args: {
          p_items: Json
          p_notes?: string
          p_project_id: string
          p_source_filename: string
          p_source_hash?: string
        }
        Returns: Json
      }
      calculate_normalization_amount: {
        Args: {
          normalization_percentage_param: number
          tender_line_item_id_param: string
        }
        Returns: {
          calculated_amount: number
          mapping_count: number
          total_budget_amount: number
        }[]
      }
      calculate_unit_item_cost: {
        Args: {
          p_labor_rate: number
          p_material_rate: number
          p_productivity_per_hour: number
        }
        Returns: number
      }
      can_access_client: {
        Args: { client_id_param: string }
        Returns: boolean
      }
      can_access_project: {
        Args: { project_id_param: string }
        Returns: boolean
      }
      can_modify_client: {
        Args: { client_id_param: string }
        Returns: boolean
      }
      can_modify_client_wbs: {
        Args: { client_id_param: string }
        Returns: boolean
      }
      can_modify_project: {
        Args: { project_id_param: string }
        Returns: boolean
      }
      compare_budget_snapshots: {
        Args: { p_snapshot_id_1: string; p_snapshot_id_2: string }
        Returns: {
          cost_diff: number
          factor_diff: number
          percent_change: number
          quantity_diff: number
          snapshot_1_cost: number
          snapshot_1_factor: number
          snapshot_1_quantity: number
          snapshot_2_cost: number
          snapshot_2_factor: number
          snapshot_2_quantity: number
          wbs_library_item_id: string
        }[]
      }
      complete_project_stage: {
        Args: { p_completion_notes?: string; p_project_stage_id: string }
        Returns: string
      }
      create_and_activate_working_version: {
        Args: { p_label?: string; p_project_id: string }
        Returns: string
      }
      create_budget_snapshot: {
        Args: {
          p_budget_version_id?: string
          p_freeze_reason?: string
          p_project_stage_id: string
        }
        Returns: string
      }
      create_budget_transfer: {
        Args: {
          from_wbs_item_id: string
          line_item_id_param: string
          project_id_param: string
          reason: string
          to_wbs_item_id: string
          transfer_amount: number
        }
        Returns: {
          budget_transfer_id: string
          error_message: string
          is_valid: boolean
        }[]
      }
      create_budget_version: {
        Args: {
          p_kind?: Database["public"]["Enums"]["budget_version_kind"]
          p_label?: string
          p_prev_version_id?: string
          p_project_id: string
          p_stage_id?: string
        }
        Returns: string
      }
      create_organization: {
        Args: { description?: string; logo_url?: string; name: string }
        Returns: Json
      }
      current_user_has_entity_access: {
        Args: {
          entity_id_param: string
          entity_type_param: Database["public"]["Enums"]["entity_type"]
        }
        Returns: boolean
      }
      current_user_has_entity_role: {
        Args: {
          entity_id_param: string
          entity_type_param: Database["public"]["Enums"]["entity_type"]
          min_role_param: Database["public"]["Enums"]["membership_role"]
        }
        Returns: boolean
      }
      diff_active_vs_items: {
        Args: { p_items: Json; p_project_id: string }
        Returns: Json
      }
      diff_budget_versions: {
        Args: { p_version_a: string; p_version_b: string }
        Returns: Json
      }
      ensure_editable_and_upsert_budget_item: {
        Args: {
          p_budget_line_item_id?: string
          p_cost_certainty?: number
          p_design_certainty?: number
          p_factor?: number
          p_labor_rate?: number
          p_material_rate?: number
          p_productivity_per_hour?: number
          p_project_id: string
          p_quantity: number
          p_remarks?: string
          p_unit?: string
          p_unit_rate?: number
          p_unit_rate_manual_override?: boolean
          p_wbs_library_item_id: string
        }
        Returns: string
      }
      find_existing_import: {
        Args: { p_project_id: string; p_source_hash: string }
        Returns: {
          budget_import_id: string
          created_at: string
          exists: boolean
          filename: string
          version_id: string
        }[]
      }
      generate_demo_project_data: {
        Args: Record<PropertyKey, never>
        Returns: Json
      }
      get_accessible_invoices: {
        Args: { project_id_param: string }
        Returns: {
          account: string
          amount: number
          created_at: string
          description: string
          invoice_date: string
          invoice_id: string
          notes: string
          period: string
          po_number: string
          post_date: string
          purchase_order_id: string
          vendor_name: string
        }[]
      }
      get_accessible_purchase_orders: {
        Args: { project_id_param: string }
        Returns: {
          co_amount: number
          created_at: string
          description: string
          original_amount: number
          po_date: string
          po_number: string
          purchase_order_id: string
          total_amount: number
          vendor_id: string
          vendor_name: string
        }[]
      }
      get_accessible_vendors: {
        Args: {
          entity_id_param: string
          entity_type_param: Database["public"]["Enums"]["entity_type"]
          user_id_param: string
        }
        Returns: {
          access_level: string
          contact_email: string
          contact_name: string
          contact_phone: string
          description: string
          is_active: boolean
          name: string
          vendor_id: string
          vendor_type: string
        }[]
      }
      get_accessible_work_packages: {
        Args: { project_id_param: string }
        Returns: {
          created_at: string
          description: string
          name: string
          project_id: string
          purchase_order_count: number
          updated_at: string
          wbs_code: string
          wbs_description: string
          wbs_library_item_id: string
          work_package_id: string
        }[]
      }
      get_active_budget_version_items: {
        Args: { p_project_id: string }
        Returns: {
          budget_line_item_id: string
          cost_certainty: number
          created_at: string
          design_certainty: number
          factor: number
          labor_rate: number
          material_rate: number
          productivity_per_hour: number
          project_id: string
          quantity: number
          remarks: string
          unit: string
          unit_rate: number
          unit_rate_manual_override: boolean
          updated_at: string
          wbs_library_item_id: string
        }[]
      }
      get_client_members: {
        Args: { _client_name: string }
        Returns: {
          access_via: string
          avatar_url: string
          created_at: string
          email: string
          full_name: string
          membership_id: string
          role: string
          updated_at: string
          user_id: string
        }[]
      }
      get_clients_with_permissions: {
        Args: { org_name_param: string }
        Returns: {
          client_id: string
          client_url: string
          created_at: string
          created_by_user_id: string
          description: string
          internal_url: string
          internal_url_description: string
          is_client_admin: boolean
          is_org_admin: boolean
          logo_url: string
          name: string
          org_id: string
          organization_name: string
          project_count: number
          updated_at: string
        }[]
      }
      get_cost_detail_data: {
        Args: { project_id_param: string }
        Returns: {
          budget_amount: number
          factor: number
          parent_item_id: string
          purchase_orders: Json
          quantity: number
          unit_rate: number
          wbs_code: string
          wbs_description: string
          wbs_level: number
          wbs_library_item_id: string
          work_packages: Json
        }[]
      }
      get_dashboard_data: {
        Args: { org_id_param: string }
        Returns: Json
      }
      get_effective_role: {
        Args: {
          entity_id_param: string
          entity_type_param: Database["public"]["Enums"]["entity_type"]
          user_id_param: string
        }
        Returns: Database["public"]["Enums"]["membership_role"]
      }
      get_entity_ancestors: {
        Args: {
          entity_id_param: string
          entity_type_param: Database["public"]["Enums"]["entity_type"]
        }
        Returns: {
          entity_id: string
          entity_type: Database["public"]["Enums"]["entity_type"]
        }[]
      }
      get_external_dashboard_data: {
        Args: Record<PropertyKey, never>
        Returns: Json
      }
      get_organization_by_name: {
        Args: { org_name_param: string }
        Returns: {
          created_at: string
          created_by_user_id: string
          description: string
          logo_url: string
          name: string
          org_id: string
          updated_at: string
        }[]
      }
      get_organization_members: {
        Args: { _org_name: string }
        Returns: {
          avatar_url: string
          created_at: string
          email: string
          full_name: string
          membership_id: string
          role: string
          updated_at: string
          user_id: string
        }[]
      }
      get_orgs_for_user: {
        Args: Record<PropertyKey, never>
        Returns: {
          created_at: string
          created_by_user_id: string
          description: string
          logo_url: string
          name: string
          org_id: string
          role: Database["public"]["Enums"]["membership_role"]
          updated_at: string
        }[]
      }
      get_project_budget_transfers: {
        Args: {
          limit_param?: number
          line_item_filter?: string
          offset_param?: number
          project_id_param: string
        }
        Returns: {
          budget_transfer_id: string
          created_at: string
          created_by_name: string
          from_wbs_code: string
          from_wbs_description: string
          line_item_description: string
          reason: string
          tender_name: string
          to_wbs_code: string
          to_wbs_description: string
          transfer_amount: number
        }[]
      }
      get_project_tenders: {
        Args: {
          limit_param?: number
          offset_param?: number
          project_id_param: string
          status_filter?: Database["public"]["Enums"]["tender_status"]
          vendor_filter?: string
        }
        Returns: {
          created_at: string
          currency_code: string
          currency_symbol: string
          current_revision_id: string
          description: string
          line_item_count: number
          notes: string
          revision_number: number
          status: Database["public"]["Enums"]["tender_status"]
          submission_date: string
          symbol_position: string
          tender_id: string
          tender_name: string
          total_amount: number
          updated_at: string
          vendor_name: string
        }[]
      }
      get_tender_budget_transfer_data: {
        Args: { project_id_param: string; tender_id_param: string }
        Returns: {
          budget_transfers: Json
          tender: Json
          wbs_items: Json
        }[]
      }
      get_tender_comparison_data: {
        Args: {
          project_id_param: string
          status_filter?: Database["public"]["Enums"]["tender_status"][]
          tender_ids?: string[]
        }
        Returns: {
          budget_amount: number
          tender_data: Json
          wbs_code: string
          wbs_description: string
          wbs_library_item_id: string
        }[]
      }
      get_tender_wbs_mapping_data: {
        Args: { project_id_param: string; tender_line_item_id_param: string }
        Returns: {
          budget_transfers: Json
          line_item: Json
          project: Json
          wbs_items: Json
        }[]
      }
      get_vendor_creation_hierarchy: {
        Args: Record<PropertyKey, never>
        Returns: {
          entity_id: string
          entity_name: string
          entity_type: string
          grandparent_entity_id: string
          grandparent_entity_name: string
          grandparent_entity_type: string
          parent_entity_id: string
          parent_entity_name: string
          parent_entity_type: string
          user_role: string
        }[]
      }
      get_wbs_items_for_work_package: {
        Args: { project_id_param: string }
        Returns: {
          code: string
          description: string
          level: number
          parent_item_id: string
          wbs_library_item_id: string
        }[]
      }
      get_work_packages_for_project: {
        Args: { project_id_param: string }
        Returns: {
          description: string
          name: string
          wbs_code: string
          work_package_id: string
        }[]
      }
      has_entity_access: {
        Args: {
          entity_id_param: string
          entity_type_param: Database["public"]["Enums"]["entity_type"]
          user_id_param: string
        }
        Returns: boolean
      }
      has_entity_role: {
        Args: {
          entity_id_param: string
          entity_type_param: Database["public"]["Enums"]["entity_type"]
          min_role_param: Database["public"]["Enums"]["membership_role"]
          user_id_param: string
        }
        Returns: boolean
      }
      import_budget_data: {
        Args: { p_items: Json; p_project_id: string }
        Returns: Json
      }
      is_client_admin: {
        Args: { client_id_param: string }
        Returns: boolean
      }
      is_construction_stage: {
        Args: { project_id_param: string }
        Returns: boolean
      }
      is_org_admin_for_project: {
        Args: { project_id_param: string }
        Returns: boolean
      }
      is_project_owner: {
        Args: { project_id_param: string }
        Returns: boolean
      }
      is_stage_ready_for_completion: {
        Args: { p_project_stage_id: string }
        Returns: boolean
      }
      list_budget_versions: {
        Args: { p_cursor?: string; p_limit?: number; p_project_id: string }
        Returns: {
          budget_version_id: string
          created_at: string
          effective_at: string
          is_active: boolean
          item_count: number
          kind: Database["public"]["Enums"]["budget_version_kind"]
          label: string
          total_cost: number
        }[]
      }
      profiles_with_client_access: {
        Args: { _client_name: string }
        Returns: {
          access_via: string
          avatar_url: string
          created_at: string
          email: string
          full_name: string
          membership_id: string
          role: string
          updated_at: string
          user_id: string
        }[]
      }
      profiles_with_project_access: {
        Args: { _project_id: string }
        Returns: {
          access_via: string
          avatar_url: string
          created_at: string
          email: string
          full_name: string
          role: string
          updated_at: string
          user_id: string
        }[]
      }
      revert_to_budget_snapshot: {
        Args: { p_budget_snapshot_id: string; p_revert_reason?: string }
        Returns: boolean
      }
      undo_budget_import: {
        Args: { p_budget_import_id: string; p_reason?: string }
        Returns: Json
      }
      upsert_budget_line_item: {
        Args: {
          p_budget_line_item_id?: string
          p_change_reason?: string
          p_cost_certainty?: number
          p_design_certainty?: number
          p_factor?: number
          p_labor_rate?: number
          p_material_rate?: number
          p_productivity_per_hour?: number
          p_project_id: string
          p_quantity: number
          p_remarks?: string
          p_unit?: string
          p_unit_rate?: number
          p_unit_rate_manual_override?: boolean
          p_wbs_library_item_id: string
        }
        Returns: string
      }
      validate_budget_transfer: {
        Args: {
          from_wbs_item_id: string
          project_id_param: string
          to_wbs_item_id: string
          transfer_amount: number
        }
        Returns: {
          error_message: string
          existing_transfers: number
          from_available_amount: number
          is_valid: boolean
          to_current_amount: number
        }[]
      }
    }
    Enums: {
      budget_version_kind: "stage" | "import" | "manual" | "system"
      checklist_item_status: "Incomplete" | "Deferred" | "Complete"
      entity_type: "organization" | "client" | "project"
      invite_resource_type: "organization" | "client" | "project"
      invite_status: "pending" | "accepted" | "revoked" | "expired" | "declined"
      membership_role: "viewer" | "editor" | "admin" | "owner"
      normalization_type: "amount" | "percentage"
      risk_status: "risk" | "pending" | "approved"
      tender_status: "submitted" | "under_review" | "selected" | "rejected"
      wbs_item_type: "Standard" | "Custom"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DatabaseWithoutInternals = Omit<Database, "__InternalSupabase">

type DefaultSchema = DatabaseWithoutInternals[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof DatabaseWithoutInternals },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof DatabaseWithoutInternals },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  graphql_public: {
    Enums: {},
  },
  public: {
    Enums: {
      budget_version_kind: ["stage", "import", "manual", "system"],
      checklist_item_status: ["Incomplete", "Deferred", "Complete"],
      entity_type: ["organization", "client", "project"],
      invite_resource_type: ["organization", "client", "project"],
      invite_status: ["pending", "accepted", "revoked", "expired", "declined"],
      membership_role: ["viewer", "editor", "admin", "owner"],
      normalization_type: ["amount", "percentage"],
      risk_status: ["risk", "pending", "approved"],
      tender_status: ["submitted", "under_review", "selected", "rejected"],
      wbs_item_type: ["Standard", "Custom"],
    },
  },
} as const

