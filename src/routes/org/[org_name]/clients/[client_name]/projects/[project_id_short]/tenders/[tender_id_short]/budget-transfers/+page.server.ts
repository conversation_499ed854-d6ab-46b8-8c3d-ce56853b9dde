import { error, fail, redirect } from '@sveltejs/kit';
import { superValidate } from 'sveltekit-superforms';
import { zod4 as zod } from 'sveltekit-superforms/adapters';
import { budgetTransferSchema, type WbsItemWithBudget } from '$lib/schemas/tender';
import { tenderUUID, projectUUID } from '$lib/schemas/project';
import type { PageServerLoad, Actions } from './$types';

type BudgetTransferWithItems = {
	budget_transfer_id: string;
	project_id: string;
	tender_line_item_id: string | null;
	from_wbs_library_item_id: string;
	to_wbs_library_item_id: string;
	transfer_amount: number;
	reason: string | null;
	created_at: string;
	updated_at: string;
	from_wbs_item: { code: string | null; description: string | null } | null;
	to_wbs_item: { code: string | null; description: string | null } | null;
};

export const load: PageServerLoad = async ({ params, locals: { supabase, user } }) => {
	if (!user) {
		throw redirect(302, '/auth/signin');
	}

	const projectId = projectUUID(params.project_id_short);
	const tenderId = tenderUUID(params.tender_id_short);

	try {
		// Get tender-level budget transfer data using RPC function
		const { data: payload, error: rpcError } = await supabase.rpc(
			'get_tender_budget_transfer_data',
			{
				project_id_param: projectId,
				tender_id_param: tenderId,
			},
		);

		if (rpcError) {
			console.error('Error loading tender budget transfer data:', rpcError);
			throw error(500, 'Failed to load tender data');
		}

		if (!payload || payload.length === 0) {
			throw error(404, 'Tender not found');
		}

		const data = payload[0];

		// Initialize form
		const transferForm = await superValidate(zod(budgetTransferSchema));

		return {
			tender: data.tender,
			wbsItems: data.wbs_items,
			budgetTransfers: data.budget_transfers,
			transferForm,
		};
	} catch (err) {
		console.error('Error loading tender budget transfer page:', err);
		throw error(500, 'Failed to load page data');
	}
};

export const actions: Actions = {
	createTransfer: async ({ request, params, locals: { supabase, user } }) => {
		if (!user) {
			throw error(401, 'Unauthorized');
		}

		const projectId = projectUUID(params.project_id_short);
		const form = await superValidate(request, zod(budgetTransferSchema));

		if (!form.valid) {
			return fail(400, { transferForm: form });
		}

		try {
			// Use RPC function for validation and creation with NULL line_item_id for tender-level
			const { data, error: rpcError } = await supabase.rpc('create_budget_transfer', {
				project_id_param: projectId,
				from_wbs_item_id: form.data.from_wbs_library_item_id,
				to_wbs_item_id: form.data.to_wbs_library_item_id,
				transfer_amount: form.data.transfer_amount,
				reason: form.data.transfer_reason || 'Budget transfer for tender analysis',
				line_item_id_param: null, // NULL for tender-level transfers
			});

			if (rpcError || !data?.[0]?.is_valid) {
				return fail(400, {
					transferForm: form,
					message: {
						type: 'error',
						text: data?.[0]?.error_message || 'Failed to create budget transfer',
					},
				});
			}

			return {
				transferForm: form,
				message: { type: 'success', text: 'Budget transfer created successfully' },
			};
		} catch (err) {
			console.error('Error creating budget transfer:', err);
			return fail(500, {
				transferForm: form,
				message: { type: 'error', text: 'Failed to create budget transfer' },
			});
		}
	},

	validateTransfer: async ({ request, params, locals: { supabase, user } }) => {
		if (!user) {
			throw error(401, 'Unauthorized');
		}

		const projectId = projectUUID(params.project_id_short);
		const formData = await request.formData();

		const fromWbsItemId = formData.get('from_wbs_library_item_id') as string;
		const toWbsItemId = formData.get('to_wbs_library_item_id') as string;
		const transferAmount = parseFloat(formData.get('transfer_amount') as string);

		if (!fromWbsItemId || !toWbsItemId || !transferAmount) {
			return fail(400, {
				message: { type: 'error', text: 'All fields are required for validation' },
			});
		}

		try {
			const { data, error: rpcError } = await supabase.rpc('validate_budget_transfer', {
				project_id_param: projectId,
				from_wbs_item_id: fromWbsItemId,
				to_wbs_item_id: toWbsItemId,
				transfer_amount: transferAmount,
			});

			if (rpcError) {
				return fail(500, {
					message: { type: 'error', text: 'Failed to validate transfer' },
				});
			}

			return {
				validation: data[0],
				message: {
					type: data[0].is_valid ? 'success' : 'error',
					text: data[0].is_valid ? 'Transfer is valid' : data[0].error_message,
				},
			};
		} catch (err) {
			console.error('Error validating budget transfer:', err);
			return fail(500, {
				message: { type: 'error', text: 'Failed to validate transfer' },
			});
		}
	},
};
