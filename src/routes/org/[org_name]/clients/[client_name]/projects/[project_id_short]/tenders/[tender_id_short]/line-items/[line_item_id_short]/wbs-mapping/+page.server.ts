import { error, fail, redirect } from '@sveltejs/kit';
import { superValidate } from 'sveltekit-superforms';
import { zod4 as zod } from 'sveltekit-superforms/adapters';
import {
	createWbsMappingSchema,
	editWbsMappingSchema,
	budgetTransferSchema,
	type TenderLineItemWithMappings,
	type WbsItemWithBudget,
} from '$lib/schemas/tender';
import { tenderLineItemUUID, projectUUID } from '$lib/schemas/project';
import { createWbsMapping, updateWbsMapping, deleteWbsMapping } from '$lib/tender_utils';
import type { PageServerLoad, Actions } from './$types';

type WbsMappingProject = {
	project_id: string;
	name: string;
	active_budget_version_id: string | null;
};

type BudgetTransferWithItems = {
	budget_transfer_id: string;
	project_id: string;
	tender_line_item_id: string | null;
	from_wbs_library_item_id: string;
	to_wbs_library_item_id: string;
	transfer_amount: number;
	reason: string | null;
	created_at: string;
	updated_at: string;
	from_wbs_item: { code: string | null; description: string | null } | null;
	to_wbs_item: { code: string | null; description: string | null } | null;
};

type TenderWbsMappingPayload = {
	project: WbsMappingProject;
	line_item: TenderLineItemWithMappings;
	wbs_items: WbsItemWithBudget[];
	budget_transfers: BudgetTransferWithItems[];
};

export const load: PageServerLoad = async ({ params, locals: { supabase, user } }) => {
	if (!user) {
		throw redirect(302, '/auth/signin');
	}

	const projectId = projectUUID(params.project_id_short);
	const lineItemId = tenderLineItemUUID(params.line_item_id_short);
	try {
		const { data, error: rpcError } = await supabase.rpc('get_tender_wbs_mapping_data', {
			project_id_param: projectId,
			tender_line_item_id_param: lineItemId,
		});

		if (rpcError) {
			console.error('Error fetching WBS mapping data:', rpcError);
			throw error(500, 'Failed to load WBS mapping data');
		}

		const payload = (data?.[0] ?? null) as TenderWbsMappingPayload | null;
		if (!payload) {
			throw error(404, 'WBS mapping data not found');
		}

		const {
			project,
			line_item: lineItem,
			wbs_items: wbsItems,
			budget_transfers: budgetTransfers,
		} = payload;

		if (!project) {
			throw error(404, 'Project not found');
		}

		// Initialize forms
		const createForm = await superValidate(zod(createWbsMappingSchema));
		const editForm = await superValidate(zod(editWbsMappingSchema));
		const transferForm = await superValidate(zod(budgetTransferSchema));

		return {
			lineItem,
			wbsItems,
			budgetTransfers,
			project,
			createForm,
			editForm,
			transferForm,
		};
	} catch (err) {
		console.error('Error loading WBS mapping page:', err);
		throw error(500, 'Failed to load page data');
	}
};

export const actions: Actions = {
	createMapping: async ({ request, params, locals: { supabase, user } }) => {
		if (!user) {
			throw error(401, 'Unauthorized');
		}

		const lineItemId = tenderLineItemUUID(params.line_item_id_short);
		const form = await superValidate(request, zod(createWbsMappingSchema));

		if (!form.valid) {
			return fail(400, { form });
		}

		try {
			await createWbsMapping(supabase, lineItemId, {
				wbs_library_item_id: form.data.wbs_library_item_id,
				coverage_percentage: form.data.coverage_percentage,
				coverage_quantity: form.data.coverage_quantity,
				notes: form.data.notes,
			});

			return {
				form,
				message: { type: 'success', text: 'WBS mapping created successfully' },
			};
		} catch (err) {
			console.error('Error creating WBS mapping:', err);
			return fail(500, {
				form,
				message: { type: 'error', text: 'Failed to create WBS mapping' },
			});
		}
	},

	updateMapping: async ({ request, locals: { supabase, user } }) => {
		if (!user) {
			throw error(401, 'Unauthorized');
		}

		const form = await superValidate(request, zod(editWbsMappingSchema));

		if (!form.valid) {
			return fail(400, { form });
		}

		const formData = await request.formData();
		const mappingId = formData.get('mapping_id') as string;

		if (!mappingId) {
			return fail(400, {
				form,
				message: { type: 'error', text: 'Mapping ID is required' },
			});
		}

		try {
			await updateWbsMapping(supabase, mappingId, {
				coverage_percentage: form.data.coverage_percentage,
				coverage_quantity: form.data.coverage_quantity,
				notes: form.data.notes,
			});

			return {
				form,
				message: { type: 'success', text: 'WBS mapping updated successfully' },
			};
		} catch (err) {
			console.error('Error updating WBS mapping:', err);
			return fail(500, {
				form,
				message: { type: 'error', text: 'Failed to update WBS mapping' },
			});
		}
	},

	deleteMapping: async ({ request, locals: { supabase, user } }) => {
		if (!user) {
			throw error(401, 'Unauthorized');
		}

		const formData = await request.formData();
		const mappingId = formData.get('mapping_id') as string;

		if (!mappingId) {
			return fail(400, {
				message: { type: 'error', text: 'Mapping ID is required' },
			});
		}

		try {
			await deleteWbsMapping(supabase, mappingId);

			return {
				message: { type: 'success', text: 'WBS mapping deleted successfully' },
			};
		} catch (err) {
			console.error('Error deleting WBS mapping:', err);
			return fail(500, {
				message: { type: 'error', text: 'Failed to delete WBS mapping' },
			});
		}
	},

	createTransfer: async ({ request, params, locals: { supabase, user } }) => {
		if (!user) {
			throw error(401, 'Unauthorized');
		}

		const projectId = projectUUID(params.project_id_short);
		const lineItemId = tenderLineItemUUID(params.line_item_id_short);
		const form = await superValidate(request, zod(budgetTransferSchema));

		if (!form.valid) {
			return fail(400, { transferForm: form });
		}

		try {
			// Use RPC function for validation and creation
			const { data, error: rpcError } = await supabase.rpc('create_budget_transfer', {
				project_id_param: projectId,
				line_item_id_param: lineItemId,
				from_wbs_item_id: form.data.from_wbs_library_item_id,
				to_wbs_item_id: form.data.to_wbs_library_item_id,
				transfer_amount: form.data.transfer_amount,
				reason: form.data.transfer_reason || 'Budget transfer for tender line item',
			});

			if (rpcError || !data?.[0]?.is_valid) {
				return fail(400, {
					transferForm: form,
					message: {
						type: 'error',
						text: data?.[0]?.error_message || 'Failed to create budget transfer',
					},
				});
			}

			return {
				transferForm: form,
				message: { type: 'success', text: 'Budget transfer created successfully' },
			};
		} catch (err) {
			console.error('Error creating budget transfer:', err);
			return fail(500, {
				transferForm: form,
				message: { type: 'error', text: 'Failed to create budget transfer' },
			});
		}
	},

	validateTransfer: async ({ request, params, locals: { supabase, user } }) => {
		if (!user) {
			throw error(401, 'Unauthorized');
		}

		const projectId = projectUUID(params.project_id_short);
		const formData = await request.formData();

		const fromWbsItemId = formData.get('from_wbs_library_item_id') as string;
		const toWbsItemId = formData.get('to_wbs_library_item_id') as string;
		const transferAmount = parseFloat(formData.get('transfer_amount') as string);

		if (!fromWbsItemId || !toWbsItemId || !transferAmount) {
			return fail(400, {
				message: { type: 'error', text: 'All fields are required for validation' },
			});
		}

		try {
			const { data, error: rpcError } = await supabase.rpc('validate_budget_transfer', {
				project_id_param: projectId,
				from_wbs_item_id: fromWbsItemId,
				to_wbs_item_id: toWbsItemId,
				transfer_amount: transferAmount,
			});

			if (rpcError) {
				return fail(500, {
					message: { type: 'error', text: 'Failed to validate transfer' },
				});
			}

			return {
				validation: data[0],
				message: {
					type: data[0].is_valid ? 'success' : 'error',
					text: data[0].is_valid ? 'Transfer is valid' : data[0].error_message,
				},
			};
		} catch (err) {
			console.error('Error validating budget transfer:', err);
			return fail(500, {
				message: { type: 'error', text: 'Failed to validate transfer' },
			});
		}
	},
};
