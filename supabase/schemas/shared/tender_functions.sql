-- Tender Analysis Functions
-- Contains RPC functions for tender analysis workflow
-- Function to get project tenders with filtering and sorting
CREATE OR REPLACE FUNCTION "public"."get_project_tenders" (
	"project_id_param" uuid,
	"status_filter" "public"."tender_status" DEFAULT NULL,
	"vendor_filter" uuid DEFAULT NULL,
	"limit_param" integer DEFAULT 50,
	"offset_param" integer DEFAULT 0
) RETURNS TABLE (
	tender_id uuid,
	vendor_name text,
	tender_name text,
	description text,
	submission_date date,
	currency_code text,
	currency_symbol text,
	symbol_position text,
	status "public"."tender_status",
	notes text,
	current_revision_id uuid,
	revision_number integer,
	line_item_count bigint,
	total_amount numeric(20, 4),
	created_at timestamptz,
	updated_at timestamptz
) LANGUAGE plpgsql SECURITY DEFINER
SET
	search_path = '' AS $$
BEGIN
    -- Verify user has access to project
    IF NOT public.current_user_has_entity_access('project', project_id_param) THEN
        RAISE EXCEPTION 'Access denied to project';
    END IF;

    RETURN QUERY
    SELECT
        t.tender_id,
        v.name as vendor_name,
        t.tender_name,
        t.description,
        t.submission_date,
        t.currency_code,
        c.symbol as currency_symbol,
        c.symbol_position,
        t.status,
        t.notes,
        tr.tender_revision_id as current_revision_id,
        tr.revision_number,
        COALESCE(line_counts.line_item_count, 0) as line_item_count,
        COALESCE(line_totals.total_amount, 0) as total_amount,
        t.created_at,
        t.updated_at
    FROM public.tender t
    JOIN public.vendor v ON t.vendor_id = v.vendor_id
    JOIN public.currency c ON t.currency_code = c.currency_code
    LEFT JOIN public.tender_revision tr ON t.tender_id = tr.tender_id AND tr.is_current = true
    LEFT JOIN (
        SELECT 
            tr_inner.tender_id,
            COUNT(tli.tender_line_item_id) as line_item_count
        FROM public.tender_revision tr_inner
        LEFT JOIN public.tender_line_item tli ON tr_inner.tender_revision_id = tli.tender_revision_id
        WHERE tr_inner.is_current = true
        GROUP BY tr_inner.tender_id
    ) line_counts ON t.tender_id = line_counts.tender_id
    LEFT JOIN (
        SELECT 
            tr_inner.tender_id,
            SUM(tli.subtotal) as total_amount
        FROM public.tender_revision tr_inner
        LEFT JOIN public.tender_line_item tli ON tr_inner.tender_revision_id = tli.tender_revision_id
        WHERE tr_inner.is_current = true
        GROUP BY tr_inner.tender_id
    ) line_totals ON t.tender_id = line_totals.tender_id
    WHERE t.project_id = project_id_param
    AND (status_filter IS NULL OR t.status = status_filter)
    AND (vendor_filter IS NULL OR t.vendor_id = vendor_filter)
    ORDER BY t.submission_date DESC, t.created_at DESC
    LIMIT limit_param
    OFFSET offset_param;
END;
$$;

ALTER FUNCTION "public"."get_project_tenders" (
	uuid,
	"public"."tender_status",
	uuid,
	integer,
	integer
) OWNER TO "postgres";

COMMENT ON FUNCTION "public"."get_project_tenders" (
	uuid,
	"public"."tender_status",
	uuid,
	integer,
	integer
) IS 'Returns tenders for a project with optional filtering by status and vendor, including summary information';

-- Function to calculate normalization amount from percentage
CREATE OR REPLACE FUNCTION "public"."calculate_normalization_amount" (
	"tender_line_item_id_param" uuid,
	"normalization_percentage_param" numeric(5, 2)
) RETURNS TABLE (
	calculated_amount numeric(20, 4),
	total_budget_amount numeric(20, 4),
	mapping_count integer
) LANGUAGE plpgsql SECURITY DEFINER
SET
	search_path = '' AS $$
DECLARE
    project_id_var uuid;
    total_budget numeric(20, 4) := 0;
    mapping_count_var integer := 0;
BEGIN
    -- Get project ID for access control
    SELECT t.project_id INTO project_id_var
    FROM public.tender_line_item tli
    JOIN public.tender_revision tr ON tli.tender_revision_id = tr.tender_revision_id
    JOIN public.tender t ON tr.tender_id = t.tender_id
    WHERE tli.tender_line_item_id = tender_line_item_id_param;

    -- Verify user has access to project
    IF NOT public.current_user_has_entity_role('project', project_id_var, 'editor') THEN
        RAISE EXCEPTION 'Access denied to project';
    END IF;

    -- Calculate total budget amount from mapped WBS items with coverage
    SELECT 
        COALESCE(SUM(
            (bvi.quantity * bvi.unit_rate * COALESCE(bvi.factor, 1)) * 
            COALESCE(twm.coverage_percentage / 100.0, 1.0)
        ), 0),
        COUNT(twm.tender_wbs_mapping_id)
    INTO total_budget, mapping_count_var
    FROM public.tender_wbs_mapping twm
    JOIN public.wbs_library_item wli ON twm.wbs_library_item_id = wli.wbs_library_item_id
    JOIN public.budget_version_item bvi ON wli.wbs_library_item_id = bvi.wbs_library_item_id
    JOIN public.budget_version bv ON bvi.budget_version_id = bv.budget_version_id
    JOIN public.project p ON bv.project_id = p.project_id
    WHERE twm.tender_line_item_id = tender_line_item_id_param
    AND p.active_budget_version_id = bv.budget_version_id;

    RETURN QUERY
    SELECT 
        (total_budget * normalization_percentage_param / 100.0) as calculated_amount,
        total_budget as total_budget_amount,
        mapping_count_var as mapping_count;
END;
$$;

ALTER FUNCTION "public"."calculate_normalization_amount" (uuid, numeric(5, 2)) OWNER TO "postgres";

COMMENT ON FUNCTION "public"."calculate_normalization_amount" (uuid, numeric(5, 2)) IS 'Calculates normalization amount from percentage based on mapped WBS budget amounts with coverage';

-- Function to validate budget transfer
CREATE OR REPLACE FUNCTION "public"."validate_budget_transfer" (
	"project_id_param" uuid,
	"from_wbs_item_id" uuid,
	"to_wbs_item_id" uuid,
	"transfer_amount" numeric(20, 4)
) RETURNS TABLE (
	is_valid boolean,
	error_message text,
	from_available_amount numeric(20, 4),
	to_current_amount numeric(20, 4),
	existing_transfers numeric(20, 4)
) LANGUAGE plpgsql SECURITY DEFINER
SET
	search_path = '' AS $$
DECLARE
    from_budget_amount numeric(20, 4);
    from_transferred_out numeric(20, 4);
    from_transferred_in numeric(20, 4);
    available_amount numeric(20, 4);
    to_budget_amount numeric(20, 4);
    to_transferred_in numeric(20, 4);
    to_transferred_out numeric(20, 4);
    current_to_amount numeric(20, 4);
    existing_transfer_amount numeric(20, 4);
BEGIN
    -- Verify user has access to project
    IF NOT public.current_user_has_entity_role('project', project_id_param, 'editor') THEN
        RAISE EXCEPTION 'Access denied to project';
    END IF;

    -- Basic input validation before looking up budget data
    IF transfer_amount IS NULL THEN
        RETURN QUERY
        SELECT
            false,
            'Transfer amount is required',
            NULL::numeric(20, 4),
            NULL::numeric(20, 4),
            NULL::numeric(20, 4);
        RETURN;
    END IF;

    IF from_wbs_item_id = to_wbs_item_id THEN
        RETURN QUERY
        SELECT
            false,
            'Source and target WBS items must be different',
            NULL::numeric(20, 4),
            NULL::numeric(20, 4),
            NULL::numeric(20, 4);
        RETURN;
    END IF;

    -- Get original budget amount for from_wbs_item
    SELECT bvi.quantity * bvi.unit_rate * COALESCE(bvi.factor, 1)
    INTO from_budget_amount
    FROM public.budget_version_item bvi
    JOIN public.budget_version bv ON bvi.budget_version_id = bv.budget_version_id
    JOIN public.project p ON bv.project_id = p.project_id
    WHERE bvi.wbs_library_item_id = from_wbs_item_id
    AND p.project_id = project_id_param
    AND p.active_budget_version_id = bv.budget_version_id;

    -- Get original budget amount for to_wbs_item
    SELECT bvi.quantity * bvi.unit_rate * COALESCE(bvi.factor, 1)
    INTO to_budget_amount
    FROM public.budget_version_item bvi
    JOIN public.budget_version bv ON bvi.budget_version_id = bv.budget_version_id
    JOIN public.project p ON bv.project_id = p.project_id
    WHERE bvi.wbs_library_item_id = to_wbs_item_id
    AND p.project_id = project_id_param
    AND p.active_budget_version_id = bv.budget_version_id;

    -- Calculate existing transfers
    SELECT
        COALESCE(SUM(CASE WHEN from_wbs_library_item_id = from_wbs_item_id THEN transfer_amount ELSE 0 END), 0),
        COALESCE(SUM(CASE WHEN to_wbs_library_item_id = from_wbs_item_id THEN transfer_amount ELSE 0 END), 0)
    INTO from_transferred_out, from_transferred_in
    FROM public.budget_transfer
    WHERE project_id = project_id_param;

    SELECT
        COALESCE(SUM(CASE WHEN to_wbs_library_item_id = to_wbs_item_id THEN transfer_amount ELSE 0 END), 0),
        COALESCE(SUM(CASE WHEN from_wbs_library_item_id = to_wbs_item_id THEN transfer_amount ELSE 0 END), 0)
    INTO to_transferred_in, to_transferred_out
    FROM public.budget_transfer
    WHERE project_id = project_id_param;

    -- Check for existing transfer between these specific items
    SELECT COALESCE(SUM(transfer_amount), 0)
    INTO existing_transfer_amount
    FROM public.budget_transfer
    WHERE project_id = project_id_param
    AND from_wbs_library_item_id = from_wbs_item_id
    AND to_wbs_library_item_id = to_wbs_item_id;

    -- Calculate available amount (original budget + transfers in - transfers out)
    available_amount := COALESCE(from_budget_amount, 0) + COALESCE(from_transferred_in, 0) - COALESCE(from_transferred_out, 0);
    current_to_amount := COALESCE(to_budget_amount, 0) + COALESCE(to_transferred_in, 0) - COALESCE(to_transferred_out, 0);

    -- Validate transfer
    RETURN QUERY
    SELECT
        CASE
            WHEN from_budget_amount IS NULL THEN false
            WHEN to_budget_amount IS NULL THEN false
            WHEN transfer_amount <= 0 THEN false
            WHEN transfer_amount > available_amount THEN false
            ELSE true
        END as is_valid,
        CASE
            WHEN from_budget_amount IS NULL THEN 'Source WBS item not found in budget'
            WHEN to_budget_amount IS NULL THEN 'Target WBS item not found in budget'
            WHEN transfer_amount <= 0 THEN 'Transfer amount must be positive'
            WHEN transfer_amount > available_amount THEN 'Transfer amount exceeds available budget'
            ELSE NULL
        END as error_message,
        available_amount as from_available_amount,
        current_to_amount as to_current_amount,
        existing_transfer_amount as existing_transfers;
END;
$$;

ALTER FUNCTION "public"."validate_budget_transfer" (uuid, uuid, uuid, numeric(20, 4)) OWNER TO "postgres";

COMMENT ON FUNCTION "public"."validate_budget_transfer" (uuid, uuid, uuid, numeric(20, 4)) IS 'Validates budget transfer between WBS items and returns validation results with current amounts';

-- Function to create budget transfer
CREATE OR REPLACE FUNCTION "public"."create_budget_transfer" (
	"project_id_param" uuid,
	"line_item_id_param" uuid,
	"from_wbs_item_id" uuid,
	"to_wbs_item_id" uuid,
	"transfer_amount" numeric(20, 4),
	"reason" text
) RETURNS TABLE (
	budget_transfer_id uuid,
	is_valid boolean,
	error_message text
) LANGUAGE plpgsql SECURITY DEFINER
SET
	search_path = '' AS $$
DECLARE
    validation_result record;
    new_transfer_id uuid;
    line_item_project_id uuid;
BEGIN
    -- Verify user has access to project
    IF NOT public.current_user_has_entity_role('project', project_id_param, 'editor') THEN
        RAISE EXCEPTION 'Access denied to project';
    END IF;

    -- Ensure the optional tender line item belongs to the same project
    IF line_item_id_param IS NOT NULL THEN
        SELECT t.project_id
        INTO line_item_project_id
        FROM public.tender_line_item tli
        JOIN public.tender_revision tr ON tli.tender_revision_id = tr.tender_revision_id
        JOIN public.tender t ON tr.tender_id = t.tender_id
        WHERE tli.tender_line_item_id = line_item_id_param;

        IF line_item_project_id IS NULL THEN
            RETURN QUERY
            SELECT
                NULL::uuid,
                false,
                'Tender line item not found';
            RETURN;
        ELSIF line_item_project_id <> project_id_param THEN
            RETURN QUERY
            SELECT
                NULL::uuid,
                false,
                'Tender line item does not belong to project';
            RETURN;
        END IF;
    END IF;

    -- Validate the transfer first
    SELECT * INTO validation_result
    FROM public.validate_budget_transfer(
        project_id_param,
        from_wbs_item_id,
        to_wbs_item_id,
        transfer_amount
    );

    -- If validation fails, return error
    IF NOT validation_result.is_valid THEN
        RETURN QUERY
        SELECT
            NULL::uuid as budget_transfer_id,
            false as is_valid,
            validation_result.error_message;
        RETURN;
    END IF;

    -- Create the budget transfer
    INSERT INTO public.budget_transfer (
        project_id,
        tender_line_item_id,
        from_wbs_library_item_id,
        to_wbs_library_item_id,
        transfer_amount,
        reason,
        created_by_user_id
    ) VALUES (
        project_id_param,
        line_item_id_param,
        from_wbs_item_id,
        to_wbs_item_id,
        transfer_amount,
        reason,
        auth.uid()
    ) RETURNING budget_transfer_id INTO new_transfer_id;

    -- Return success
    RETURN QUERY
    SELECT
        new_transfer_id,
        true as is_valid,
        NULL::text as error_message;
END;
$$;

ALTER FUNCTION "public"."create_budget_transfer" (uuid, uuid, uuid, uuid, numeric(20, 4), text) OWNER TO "postgres";

COMMENT ON FUNCTION "public"."create_budget_transfer" (uuid, uuid, uuid, uuid, numeric(20, 4), text) IS 'Creates a budget transfer after validation and returns the result';

-- Function to fetch WBS mapping data for a tender line item
CREATE OR REPLACE FUNCTION "public"."get_tender_wbs_mapping_data" (
	"project_id_param" uuid,
	"tender_line_item_id_param" uuid
) RETURNS TABLE (
	project jsonb,
	line_item jsonb,
	wbs_items jsonb,
	budget_transfers jsonb
) LANGUAGE plpgsql SECURITY DEFINER
SET
	search_path = '' AS $$
DECLARE
	project_json jsonb;
	line_item_json jsonb;
	wbs_items_json jsonb := '[]'::jsonb;
	budget_transfers_json jsonb := '[]'::jsonb;
	v_active_budget_version_id uuid;
BEGIN
	-- Verify project access and load minimal project data
	SELECT
		row_to_json(project_row)::jsonb,
		project_row.active_budget_version_id
	INTO
		project_json,
		v_active_budget_version_id
	FROM (
		SELECT
			project_id,
			name,
			active_budget_version_id
		FROM public.project
		WHERE project_id = project_id_param
	) AS project_row;

	IF project_json IS NULL THEN
		RAISE EXCEPTION 'Project not found';
	END IF;

	IF NOT public.current_user_has_entity_access('project', project_id_param) THEN
		RAISE EXCEPTION 'Access denied to project';
	END IF;

	-- Load tender line item with existing mappings
	SELECT
		to_jsonb(line_item_data)
	INTO line_item_json
	FROM (
		SELECT
			tli.*,
			COALESCE(
				(
					SELECT jsonb_agg(
						jsonb_build_object(
							'tender_wbs_mapping_id', twm.tender_wbs_mapping_id,
							'tender_line_item_id', twm.tender_line_item_id,
							'wbs_library_item_id', twm.wbs_library_item_id,
							'coverage_percentage', twm.coverage_percentage,
							'coverage_quantity', twm.coverage_quantity,
							'notes', twm.notes,
							'created_at', twm.created_at,
							'updated_at', twm.updated_at,
							'wbs_library_item', jsonb_build_object(
								'code', wli.code,
								'description', wli.description,
								'level', wli.level
							)
						)
						ORDER BY twm.created_at
					)
					FROM public.tender_wbs_mapping twm
					JOIN public.wbs_library_item wli ON wli.wbs_library_item_id = twm.wbs_library_item_id
					WHERE twm.tender_line_item_id = tli.tender_line_item_id
				),
				'[]'::jsonb
			) AS tender_wbs_mapping
		FROM public.tender_line_item tli
		JOIN public.tender_revision tr ON tli.tender_revision_id = tr.tender_revision_id
		JOIN public.tender t ON tr.tender_id = t.tender_id
		WHERE tli.tender_line_item_id = tender_line_item_id_param
			AND t.project_id = project_id_param
		LIMIT 1
	) AS line_item_data;

	IF line_item_json IS NULL THEN
		RAISE EXCEPTION 'Tender line item not found';
	END IF;

	-- Load ALL WBS items for the project to ensure complete hierarchy, with budget information where available
	-- This includes standard library items, client custom items, and project custom items
	WITH project_info AS (
		SELECT
			p.project_id,
			p.client_id,
			p.wbs_library_id,
			p.active_budget_version_id
		FROM public.project p
		WHERE p.project_id = project_id_param
	)
	SELECT
		COALESCE(jsonb_agg(wbs_item ORDER BY (wbs_item ->> 'code')), '[]'::jsonb)
	INTO wbs_items_json
	FROM (
		SELECT DISTINCT ON (wli.wbs_library_item_id)
			jsonb_build_object(
				'wbs_library_item_id', wli.wbs_library_item_id,
				'wbs_library_id', wli.wbs_library_id,
				'level', wli.level,
				'in_level_code', wli.in_level_code,
				'parent_item_id', wli.parent_item_id,
				'code', wli.code,
				'description', wli.description,
				'cost_scope', wli.cost_scope,
				'item_type', wli.item_type,
				'client_id', wli.client_id,
				'project_id', wli.project_id,
				'created_at', wli.created_at,
				'updated_at', wli.updated_at,
				'budget_amount', COALESCE(bvi.quantity, 0) * COALESCE(bvi.unit_rate, 0) * COALESCE(bvi.factor, 1),
				'budget_quantity', bvi.quantity,
				'budget_unit_rate', bvi.unit_rate,
				'unit', bvi.unit
			) AS wbs_item
	FROM public.wbs_library_item wli
	CROSS JOIN project_info pi
	LEFT JOIN public.budget_version_item bvi ON bvi.wbs_library_item_id = wli.wbs_library_item_id
		AND bvi.budget_version_id = pi.active_budget_version_id
	WHERE (
		-- Standard library items
		(wli.wbs_library_id = pi.wbs_library_id AND wli.item_type = 'Standard')
		OR
		-- Client custom items (not project-specific)
		(wli.client_id = pi.client_id AND wli.item_type = 'Custom' AND wli.project_id IS NULL)
		OR
		-- Project-specific custom items
		(wli.client_id = pi.client_id AND wli.item_type = 'Custom' AND wli.project_id = pi.project_id)
	)
	ORDER BY
		wli.wbs_library_item_id,
		bvi.created_at DESC
	) AS wbs_items;

	-- Load budget transfers for the project
	SELECT
		COALESCE(
			jsonb_agg(
				jsonb_build_object(
					'budget_transfer_id', bt.budget_transfer_id,
					'project_id', bt.project_id,
					'tender_line_item_id', bt.tender_line_item_id,
					'from_wbs_library_item_id', bt.from_wbs_library_item_id,
					'to_wbs_library_item_id', bt.to_wbs_library_item_id,
					'transfer_amount', bt.transfer_amount,
					'reason', bt.reason,
					'created_at', bt.created_at,
					'updated_at', bt.updated_at,
					'from_wbs_item', jsonb_build_object(
						'code', from_wli.code,
						'description', from_wli.description
					),
					'to_wbs_item', jsonb_build_object(
						'code', to_wli.code,
						'description', to_wli.description
					)
				)
				ORDER BY bt.created_at DESC
			),
			'[]'::jsonb
		)
	INTO budget_transfers_json
	FROM public.budget_transfer bt
	LEFT JOIN public.wbs_library_item from_wli ON from_wli.wbs_library_item_id = bt.from_wbs_library_item_id
	LEFT JOIN public.wbs_library_item to_wli ON to_wli.wbs_library_item_id = bt.to_wbs_library_item_id
	WHERE bt.project_id = project_id_param;

	RETURN QUERY
	SELECT
		project_json,
		line_item_json,
		wbs_items_json,
		budget_transfers_json;
END;
$$;

ALTER FUNCTION "public"."get_tender_wbs_mapping_data" (uuid, uuid) OWNER TO "postgres";

COMMENT ON FUNCTION "public"."get_tender_wbs_mapping_data" (uuid, uuid) IS 'Returns project, tender line item, related WBS items, and budget transfers for WBS mapping UI.';
